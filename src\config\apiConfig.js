// API Configuration
// Centralized configuration for all API endpoints

// Production server URL
export const PROD_BASE_URL = 'https://kotaby.duckdns.org';

// Development server URL  
export const DEV_BASE_URL = 'http://localhost:9090';

// Current active base URL - change this to switch between environments
export const BASE_URL = DEV_BASE_URL;

// Tasks API base URL (currently using localhost)
export const TASKS_BASE_URL = DEV_BASE_URL;

// API Endpoints
export const API_ENDPOINTS = {
  // User authentication endpoints
  LOGIN: `${BASE_URL}/users/login`,
  REGISTER: `${BASE_URL}/users/register`,
  GET_USER: (userId) => `${BASE_URL}/users/${userId}`,
  GET_ALL_USERS: `${BASE_URL}/users`,
  UPLOAD_PHOTO: (userId) => `${BASE_URL}/users/${userId}/photo`,
  
  // Reading plan task endpoints
  CREATE_PLAN: `${TASKS_BASE_URL}/tasks/create-reading-plan`,
  COMPLETE_TASK: (taskId) => `${TASKS_BASE_URL}/tasks/${taskId}/complete`,
  GET_USER_TASKS: (userId) => `${TASKS_BASE_URL}/tasks/user/${userId}`,
  DELETE_USER_PLAN: (userId) => `${TASKS_BASE_URL}/tasks/user/${userId}`,
  GET_USER_SUMMARY: (userId) => `${TASKS_BASE_URL}/tasks/user/${userId}/summary`,

  // Streak tracking endpoints
  UPDATE_STREAK: (userId) => `${BASE_URL}/streak/update/${userId}`,
  GET_STREAK: (userId) => `${BASE_URL}/streak/${userId}`,

  // Hadith endpoints
  SEARCH_HADITH: (hadithText) => `${BASE_URL}/hadith/search?hadithText=${encodeURIComponent(hadithText)}`,
  RANDOM_HADITH: `${BASE_URL}/hadith/random`
};

export default {
  PROD_BASE_URL,
  DEV_BASE_URL,
  BASE_URL,
  TASKS_BASE_URL,
  API_ENDPOINTS
};
