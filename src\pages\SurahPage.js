// src/pages/SurahPage.js
import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FaArrowLeft } from 'react-icons/fa';

function SurahPage() {
  const { page } = useParams();
  const navigate = useNavigate();

  return (
    <div style={{ marginTop: '100px', textAlign: 'center' }}>
      <button
        onClick={() => navigate(-1)}
        style={{
          marginBottom: '20px',
          background: '#bfa131',
          color: 'white',
          padding: '10px 20px',
          border: 'none',
          borderRadius: '8px',
          cursor: 'pointer'
        }}
      >
        <FaArrowLeft /> Back
      </button>
      <h2 style={{ color: '#bfa131' }}>Quran Page {page}</h2>
      <img
        src={`https://cdn.islamic.network/quran/pages/${page}.png`}
        alt={`Quran Page ${page}`}
        style={{
          maxWidth: '100%',
          height: 'auto',
          boxShadow: '0 0 20px rgba(0,0,0,0.2)',
          border: '2px solid #d4af37'
        }}
      />
    </div>
  );
}

export default SurahPage;
    