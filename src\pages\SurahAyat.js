import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FaChevronLeft, FaChevronRight, FaArrowLeft } from 'react-icons/fa';
import { SURAH_PAGE_MAPPING, getPagesForSurah, getPagePairs, getSurahByPage } from '../utils/surahPageMapping';
import { API_ENDPOINTS } from '../config/apiConfig';
import { getStoredUserData } from '../utils/userApi';

function SurahAyat() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [currentSurahId, setCurrentSurahId] = useState(parseInt(id));
  const [currentPairIndex, setCurrentPairIndex] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Get current Surah info and pages
  const currentSurah = SURAH_PAGE_MAPPING[currentSurahId];
  const pages = getPagesForSurah(currentSurahId);
  const pagePairs = getPagePairs(pages);
  const currentPair = pagePairs[currentPairIndex];

  // Update Surah when URL changes
  useEffect(() => {
    const newSurahId = parseInt(id);
    setCurrentSurahId(newSurahId);

    // Check if there's a page parameter in the URL
    const urlParams = new URLSearchParams(window.location.search);
    const targetPage = urlParams.get('page');

    if (targetPage) {
      // Find which pair contains this page
      const pages = getPagesForSurah(newSurahId);
      const pairs = getPagePairs(pages);
      const pairIndex = pairs.findIndex(pair =>
        pair.rightPage === parseInt(targetPage) || pair.leftPage === parseInt(targetPage)
      );

      if (pairIndex !== -1) {
        setCurrentPairIndex(pairIndex);
      } else {
        setCurrentPairIndex(0);
      }
    } else {
      setCurrentPairIndex(0);
    }

    // Send streak update when Surah is selected
    updateStreakForSurah(newSurahId);
  }, [id]);

  // Function to update streak when Surah is accessed
  const updateStreakForSurah = async (surahId) => {
    try {
      const userData = getStoredUserData();
      if (!userData || !userData.id) return;

      const response = await fetch(API_ENDPOINTS.UPDATE_STREAK(userData.id), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          date: new Date().toISOString(),
          lastSurah: surahId,
          lastAyah: SURAH_PAGE_MAPPING[surahId]?.startPage || 1
        }),
      });

      if (response.ok) {
        console.log('✅ Streak updated for Surah:', surahId);
      } else {
        console.error('❌ Failed to update streak');
      }
    } catch (error) {
      console.error('❌ Error updating streak:', error);
    }
  };

  // Function to update streak when swiping to different Surah
  const updateStreakForPage = async (pageNumber) => {
    try {
      const userData = getStoredUserData();
      if (!userData || !userData.id) return;

      const surahInfo = getSurahByPage(pageNumber);
      if (!surahInfo) return;

      const response = await fetch(API_ENDPOINTS.UPDATE_STREAK(userData.id), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          date: new Date().toISOString(),
          lastSurah: surahInfo.id,
          lastAyah: pageNumber
        }),
      });

      if (response.ok) {
        console.log('✅ Streak updated for page:', pageNumber);
      }
    } catch (error) {
      console.error('❌ Error updating streak for page:', error);
    }
  };

  // Navigation functions
  const handlePrevPair = () => {
    if (currentPairIndex > 0) {
      setCurrentPairIndex(prev => prev - 1);
    } else {
      // Go to previous Surah
      if (currentSurahId > 1) {
        const prevSurahId = currentSurahId - 1;
        const prevPages = getPagesForSurah(prevSurahId);
        const prevPairs = getPagePairs(prevPages);
        setCurrentSurahId(prevSurahId);
        setCurrentPairIndex(prevPairs.length - 1);

        // Update streak for the new Surah
        updateStreakForPage(prevPages[prevPages.length - 1]);
      }
    }
  };

  const handleNextPair = () => {
    if (currentPairIndex < pagePairs.length - 1) {
      setCurrentPairIndex(prev => prev + 1);
    } else {
      // Go to next Surah
      if (currentSurahId < 114) {
        const nextSurahId = currentSurahId + 1;
        setCurrentSurahId(nextSurahId);
        setCurrentPairIndex(0);

        // Update streak for the new Surah
        const nextPages = getPagesForSurah(nextSurahId);
        updateStreakForPage(nextPages[0]);
      }
    }
  };

  // Get current Surah name based on the right page
  const getCurrentSurahName = () => {
    if (!currentPair) return currentSurah?.name || '';

    const rightPageSurah = getSurahByPage(currentPair.rightPage);
    return rightPageSurah?.name || currentSurah?.name || '';
  };

  if (loading) return <p style={{ marginTop: '100px' }}>Loading...</p>;
  if (error) return <p style={{ marginTop: '100px', color: 'red' }}>{error}</p>;
  if (!currentSurah || !currentPair) return <p style={{ marginTop: '100px' }}>No data found for this surah.</p>;



  return (
    <div style={{
      marginTop: '120px',
      padding: '20px',
      maxWidth: '1200px',
      margin: '0 auto',
      position: 'relative'
    }}>
      {/* Back to Surahs Button */}
      <div style={{
        position: 'absolute',
        top: '80px',
        left: '24px',
      }}>
        <button
          onClick={() => navigate('/surahs')}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            background: '#8B4513',
            color: 'white',
            border: 'none',
            padding: '10px 15px',
            borderRadius: '5px',
            cursor: 'pointer',
            fontSize: '16px',
            boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
          }}
        >
          <FaArrowLeft /> Back to Surahs
        </button>
      </div>

      {/* Surah Header */}
      <div style={{
        textAlign: 'center',
        marginBottom: '30px',
        borderBottom: '2px solid #d4af37',
        paddingBottom: '15px'
      }}>
        <h1 style={{
          color: '#d4af37',
          marginBottom: '10px',
          fontFamily: 'serif'
        }}>
          {getCurrentSurahName()}
        </h1>
        <h2 style={{
          color: '#8B4513',
          fontFamily: 'serif',
          fontSize: '32px'
        }}>
          {currentSurah?.arabicName}
        </h2>
      </div>

      {/* Quran Pages with Side Navigation */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        position: 'relative',
        minHeight: '800px',
        gap: '20px'
      }}>
        {/* Left Navigation Button (Next Page for RTL) */}
        <button
          onClick={handleNextPair}
          disabled={currentPairIndex >= pagePairs.length - 1 && currentSurahId >= 114}
          style={{
            background: 'transparent',
            color: (currentPairIndex >= pagePairs.length - 1 && currentSurahId >= 114) ? '#ccc' : '#8B4513',
            border: 'none',
            fontSize: '24px',
            cursor: (currentPairIndex >= pagePairs.length - 1 && currentSurahId >= 114) ? 'not-allowed' : 'pointer',
            padding: '10px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10,
            position: 'absolute',
            left: '-20px',
            top: '50%',
            transform: 'translateY(-50%)',
            width: '40px',
            height: '40px',
            borderRadius: '50%',
            boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
            backgroundColor: 'white'
          }}
        >
          <FaChevronLeft />
        </button>

        {/* Quran Page Images */}
        <div style={{
          display: 'flex',
          gap: '20px',
          width: '100%',
          justifyContent: 'center',
          alignItems: 'flex-start'
        }}>
          {/* Right Page (Always displayed, odd page numbers) */}
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center'
          }}>
            <img
              src={`https://cdn.islamic.network/quran/pages/${currentPair.rightPage}.png`}
              alt={`Quran Page ${currentPair.rightPage}`}
              style={{
                maxWidth: '400px',
                width: '100%',
                border: '2px solid #d4af37',
                borderRadius: '8px',
                boxShadow: '0 4px 20px rgba(0,0,0,0.15)'
              }}
            />
            <p style={{
              marginTop: '10px',
              fontSize: '16px',
              fontWeight: 'bold',
              color: '#8B4513',
              textAlign: 'center'
            }}>
              Page {currentPair.rightPage}
            </p>
          </div>

          {/* Left Page (Only if exists, even page numbers) */}
          {currentPair.leftPage && (
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center'
            }}>
              <img
                src={`https://cdn.islamic.network/quran/pages/${currentPair.leftPage}.png`}
                alt={`Quran Page ${currentPair.leftPage}`}
                style={{
                  maxWidth: '400px',
                  width: '100%',
                  border: '2px solid #d4af37',
                  borderRadius: '8px',
                  boxShadow: '0 4px 20px rgba(0,0,0,0.15)'
                }}
              />
              <p style={{
                marginTop: '10px',
                fontSize: '16px',
                fontWeight: 'bold',
                color: '#8B4513',
                textAlign: 'center'
              }}>
                Page {currentPair.leftPage}
              </p>
            </div>
          )}
        </div>

        {/* Right Navigation Button (Previous Page for RTL) */}
        <button
          onClick={handlePrevPair}
          disabled={currentPairIndex <= 0 && currentSurahId <= 1}
          style={{
            background: 'transparent',
            color: (currentPairIndex <= 0 && currentSurahId <= 1) ? '#ccc' : '#8B4513',
            border: 'none',
            fontSize: '24px',
            cursor: (currentPairIndex <= 0 && currentSurahId <= 1) ? 'not-allowed' : 'pointer',
            padding: '10px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10,
            position: 'absolute',
            right: '-20px',
            top: '50%',
            transform: 'translateY(-50%)',
            width: '40px',
            height: '40px',
            borderRadius: '50%',
            boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
            backgroundColor: 'white'
          }}
        >
          <FaChevronRight />
        </button>
      </div>

      {/* Page Information */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        marginTop: '20px',
        alignItems: 'center'
      }}>
        <div style={{
          color: '#8B4513',
          fontStyle: 'italic',
          fontSize: '16px',
          textAlign: 'center'
        }}>
          {getCurrentSurahName()} • Page {currentPair.rightPage}{currentPair.leftPage ? ` - ${currentPair.leftPage}` : ''} •
          Pair {currentPairIndex + 1} of {pagePairs.length}
        </div>
      </div>

      {/* Back to Surahs Button (Bottom) */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        marginTop: '30px'
      }}>
        <button
          onClick={() => navigate('/surahs')}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            background: '#d4af37',
            color: 'white',
            border: 'none',
            padding: '12px 20px',
            borderRadius: '5px',
            cursor: 'pointer',
            fontSize: '16px',
            boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
          }}
        >
          <FaArrowLeft /> Back to All Surahs
        </button>
      </div>
    </div>
  );
}

export default SurahAyat;
