import React, { useState, useEffect } from 'react';
import KaabaImg from '../assets/kaaba.png';
import MadinahImg from '../assets/madinah.png';
import { useNavigate } from 'react-router-dom';
import { getSurahInfo, getSurahStartPage } from '../utils/surahPageMapping';
import { getStoredUserData } from '../utils/userApi';

function Surahs() {
  const [surahs, setSurahs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    async function fetchSurahs() {
      try {
        setLoading(true);
        setError(null);
        console.log('Fetching surahs data from Alquran.cloud API...');

        // Using the Alquran.cloud API which is more reliable and doesn't require authentication
        const response = await fetch('https://api.alquran.cloud/v1/surah');

        if (!response.ok) {
          throw new Error(`API returned status code ${response.status}`);
        }

        const data = await response.json();
        console.log('Surah data response:', data);

        if (!data || !data.data || data.code !== 200) {
          throw new Error('Invalid data format received from API');
        }

        const formattedSurahs = data.data.map(surah => ({
          number: surah.number,
          englishName: surah.englishName,
          arabicName: surah.name,
          verses: surah.numberOfAyahs,
          revelationType: surah.revelationType,
        }));

        console.log('Formatted surahs:', formattedSurahs);
        setSurahs(formattedSurahs);
      } catch (error) {
        console.error('API fetch failed:', error);
        setError(`Failed to fetch Quran data: ${error.message}`);
      } finally {
        setLoading(false);
      }
    }

    fetchSurahs();
  }, []);

  // Test function to verify array lookup
  const testArrayLookup = () => {
    console.log('🧪 TESTING ARRAY LOOKUP:');
    for (let i = 1; i <= 10; i++) {
      const startPage = getSurahStartPage(i);
      console.log(`Surah ${i} -> Start Page: ${startPage}`);
    }
  };

  // Call test on component mount (remove this after testing)
  useEffect(() => {
    if (surahs.length > 0) {
      testArrayLookup();
    }
  }, [surahs]);

  // Function to update last read state when Surah is clicked
  const handleSurahClick = async (surah) => {
    try {
      const userData = getStoredUserData();
      if (!userData || !userData.id) {
        console.log('No user data found, navigating without updating last read');
        navigate(`/surahayat/${surah.number}`);
        return;
      }

      // Get the starting page for this Surah from the array
      const startPage = getSurahStartPage(surah.number);

      console.log(`📖 Updating last read: Surah ${surah.number}, Start Page ${startPage}`);

      const requestBody = {
        id: userData.id,
        userName: userData.userName,
        email: userData.email,
        password: userData.password,
        image: userData.image || "",
        dateOfBirth: userData.dateOfBirth || new Date().toISOString(),
        nationality: userData.nationality || "",
        lastSurah: surah.number,    // ✅ Surah number
        lastAyah: startPage         // ✅ Start page from the array
      };

      // Send POST request to update user's last read state
      const response = await fetch(`http://localhost:9090/streak/update/${userData.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (response.ok) {
        console.log('✅ Last read state updated successfully');
        console.log(`📖 Sent: LastSurah=${surah.number}, LastAyah=${startPage}`);

        // Log the response to check if backend saved correctly
        const responseData = await response.json();
        console.log('� Backend returned:', `LastSurah=${responseData.lastSurah}, LastAyah=${responseData.lastAyah}`);

        if (responseData.lastSurah !== surah.number || responseData.lastAyah !== startPage) {
          console.warn('⚠️ Backend did not save the correct values!');
          console.warn(`Expected: LastSurah=${surah.number}, LastAyah=${startPage}`);
          console.warn(`Got: LastSurah=${responseData.lastSurah}, LastAyah=${responseData.lastAyah}`);
        }
      } else {
        console.error('❌ Failed to update last read state');
        console.error('❌ Response status:', response.status);
      }
    } catch (error) {
      console.error('❌ Error updating last read state:', error);
    }

    // Navigate to the Surah page
    navigate(`/surahayat/${surah.number}`);
  };

  return (
    <div style={{ padding: '30px', marginTop: '75px', backgroundColor: '#f9f9f9' }}>
      <h1 style={{ textAlign: 'center', marginBottom: '30px', color: '#bfa131' }}>
        Quran Surahs
      </h1>

      {loading && (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <p style={{ fontSize: '18px', color: '#bfa131' }}>Loading Quran surahs...</p>
        </div>
      )}

      {error && (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <p style={{ fontSize: '18px', color: 'red' }}>{error}</p>
          <button
            onClick={() => window.location.reload()}
            style={{
              marginTop: '20px',
              padding: '10px 20px',
              backgroundColor: '#bfa131',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            Try Again
          </button>
        </div>
      )}

      {!loading && !error && surahs.length === 0 && (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <p style={{ fontSize: '18px', color: '#bfa131' }}>No surahs found. Please try again later.</p>
        </div>
      )}

      {!loading && !error && surahs.length > 0 && (
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
        {surahs.map(surah => (
          <div key={surah.number}
            onClick={() => handleSurahClick(surah)}
            style={{
              backgroundColor: '#f5f5f5',
              padding: '10px 20px',
              borderRadius: '15px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
              textAlign: 'center',
              height: '100px',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between',
              transition: 'all 0.3s ease',
              cursor: 'pointer',
            }}
            onMouseEnter={e => e.currentTarget.style.transform = 'scale(1.03)'}
            onMouseLeave={e => e.currentTarget.style.transform = 'scale(1)'}>

            <div style={{ display: 'flex', alignItems: 'center' }}>
              <img
                src={surah.revelationType === 'Meccan' ? KaabaImg : MadinahImg}
                alt={surah.revelationType}
                style={{ width: '55px', height: '55px', marginRight: '10px', marginTop: '10px' }}
              />
              <div style={{ flex: 1, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <h4 style={{ margin: 0, color: '#bfa131', fontSize: '20px' }}>
                  {surah.number}. {surah.englishName}
                </h4>
                <h4 style={{ margin: 0, color: '#d4af37', fontSize: '22px', textAlign: 'right' }}>
                  {surah.arabicName}
                </h4>
              </div>
            </div>

            <p style={{ margin: 0, fontSize: '14px', color: '#555', marginTop: '-10px' }}>
              {surah.verses} ayahs
            </p>
          </div>
        ))}
      </div>
      )}
    </div>
  );
}

export default Surahs;
