import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Login from './pages/Login';
import Signup from './pages/Signup';
import Home from './pages/Home';
import Hadith from './pages/Hadith';
import Surahs from './pages/Surahs';
import SurahAyat from './pages/SurahAyat';
import Tafser from './pages/Tafser';
import Tasmee from './pages/Tasmee';
import Navbar from './components/Navbar';
import { useState, useEffect } from 'react';
import { isUserAuthenticated, logoutUser, forceLogout } from './utils/userApi';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  console.log('App render - isAuthenticated:', isAuthenticated, 'isLoading:', isLoading);

  // Add debug function to window for manual testing
  useEffect(() => {
    window.forceLogout = forceLogout;
    window.clearAuth = () => {
      logoutUser();
      setIsAuthenticated(false);
      console.log('Authentication cleared manually');
    };
  }, []);

  // Check authentication status on app load
  useEffect(() => {
    const checkAuth = () => {
      try {
        // First, let's see what's in localStorage
        console.log('LocalStorage user data:', localStorage.getItem('user'));
        console.log('LocalStorage userData:', localStorage.getItem('userData'));

        const authenticated = isUserAuthenticated();
        console.log('Authentication check result:', authenticated);

        if (!authenticated) {
          // Clear any invalid stored data
          console.log('Clearing invalid stored data...');
          logoutUser();
        }

        setIsAuthenticated(authenticated);
      } catch (error) {
        console.error('Error checking authentication:', error);
        // Clear any corrupted data and set as not authenticated
        console.log('Clearing corrupted data...');
        logoutUser();
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Show loading screen while checking authentication
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        backgroundColor: '#f5f5f5'
      }}>
        <div style={{
          color: '#d4af37',
          fontSize: '18px',
          fontWeight: 'bold'
        }}>
          Loading...
        </div>
      </div>
    );
  }

  const handleLogout = () => {
    console.log('Logging out user...');
    logoutUser(); // Clear stored data
    setIsAuthenticated(false);
  };

  return (
    <Router>
      {isAuthenticated && <Navbar onLogout={handleLogout} />}
      <Routes>
        <Route path="/" element={<Navigate to="/login" />} />
        <Route path="/login" element={!isAuthenticated ? <Login setIsAuthenticated={setIsAuthenticated} /> : <Navigate to="/home" />} />
        <Route path="/signup" element={!isAuthenticated ? <Signup setIsAuthenticated={setIsAuthenticated} /> : <Navigate to="/home" />} />
        <Route path="/home" element={isAuthenticated ? <Home /> : <Navigate to="/login" />} />
        <Route path="/hadith" element={isAuthenticated ? <Hadith /> : <Navigate to="/login" />} />
        <Route path="/surahs" element={isAuthenticated ? <Surahs /> : <Navigate to="/login" />} />
        <Route path="/surahayat/:id" element={isAuthenticated ? <SurahAyat /> : <Navigate to="/login" />} />
        <Route path="/tasmee" element={isAuthenticated ? <Tasmee /> : <Navigate to="/login" />} />
        <Route path="/tafser" element={isAuthenticated ? <Tafser /> : <Navigate to="/login" />} />
      </Routes>
    </Router>
  );
}

export default App;
