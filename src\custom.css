/* Full dark background */
body {
    background-color: #f5f5f5;
    margin: 0;
    font-family: 'Poppins', sans-serif;
  }
  
  /* Form container */
  .form-container {
    background-color: #141638;
    padding: 40px 30px;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    width: 100%;
    max-width: 450px;
  }
  
  /* Main heading */
  .form-container h2 {
    color: #a377d7;
    font-weight: 600;
    font-size: 20px;
  }
/* Sub text */
.form-container p.subtitle {
    color: #a377d7;
    margin-bottom: 80px; /* More margin under "Login to continue" */
  }
  
  
  /* Label */
  .form-container label {
    color: rgba(163, 119, 215, 0.7);
    margin-bottom: 5px;
    font-size: 13px;
    transition: all 0.4s ease;
    transform-origin: left;
  }
  
  /* Floating effect */
  .form-floating > .form-control:focus ~ label,
  .form-floating > .form-control:not(:placeholder-shown) ~ label {
    transform: scale(0.70) translateY(-1.1rem) translateX(0.15rem);
    color: #a377d7;
  }
  
  /* Inputs */
  .form-container input {
    background-color: #1f224a;
    border: 1px solid #3e4073;
    border-radius: 8px;
    color: #d4d4f7;
  }
  
  .form-container input::placeholder {
    color: transparent; /* hide placeholder to trigger floating */
  }
  
  .form-container input:focus {
    border-color: #672a91;
    box-shadow: 0 0 0 0.2rem rgba(103, 42, 145, 0.25);
  }
  
  /* Button */
  .btn-custom {
    background-color: #672a91;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px;
    font-weight: 500;
    transition: background-color 0.3s;
  }
  
  .btn-custom:hover {
    background-color: #5b227f;
  }
  
  /* Forgot Password & Signup */
  .signup-link, .forgot-link {
    color: #8b5fc2;
    text-decoration: none;
  }
  
  .signup-link:hover, .forgot-link:hover {
    text-decoration: underline;
  }
  
  .forgot-link {
    display: inline-block;
    margin-top: 20px;
    margin-bottom: 20px;
  }
  
  .signup-text {
    color: white;
  }
  