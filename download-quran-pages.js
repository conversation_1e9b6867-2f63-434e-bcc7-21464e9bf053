const fs = require('fs');
const https = require('https');
const path = require('path');

// Create the directory if it doesn't exist
const dir = path.join(__dirname, 'public', 'images', 'quran-pages');
if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
}

// Function to download a single image
function downloadImage(pageNumber) {
    return new Promise((resolve, reject) => {
        // Using Quran.com API format
        const url = `https://api.quran.com/api/v4/page_images/${pageNumber}?width=1000`;
        const filePath = path.join(dir, `page${pageNumber}.jpg`);

        // Skip if file already exists
        if (fs.existsSync(filePath)) {
            console.log(`Page ${pageNumber} already exists, skipping...`);
            resolve();
            return;
        }

        const options = {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        };

        https.get(url, options, (response) => {
            if (response.statusCode !== 200) {
                reject(new Error(`Failed to download page ${pageNumber}: ${response.statusCode}`));
                return;
            }

            const file = fs.createWriteStream(filePath);
            response.pipe(file);

            file.on('finish', () => {
                file.close();
                console.log(`Downloaded page ${pageNumber}`);
                resolve();
            });
        }).on('error', (err) => {
            fs.unlink(filePath, () => {}); // Delete the file if there was an error
            reject(err);
        });
    });
}

// Function to download all pages with delay between requests
async function downloadAllPages() {
    const totalPages = 604; // Total number of pages in the Quran
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

    console.log('Starting download of Quran pages...');
    
    for (let i = 1; i <= totalPages; i++) {
        try {
            await downloadImage(i);
            // Add a small delay between downloads to avoid overwhelming the server
            await delay(500); // Increased delay to be more respectful to the server
        } catch (error) {
            console.error(`Error downloading page ${i}:`, error.message);
            // Continue with next page even if one fails
            continue;
        }
    }
    
    console.log('Download completed!');
}

// Start the download process
downloadAllPages().catch(console.error); 