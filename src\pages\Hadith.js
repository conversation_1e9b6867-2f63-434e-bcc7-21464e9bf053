import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaArrowLeft } from 'react-icons/fa';

function Hadith() {
  const navigate = useNavigate();
  const [hadithText, setHadithText] = useState('');
  const [classification, setClassification] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Function to classify hadith
  const classifyHadith = async () => {
    if (!hadithText.trim()) {
      setError('Please enter a hadith to classify');
      return;
    }

    setLoading(true);
    setError(null);
    setClassification(null);

    try {
      // Simulate API call with a timeout
      setTimeout(() => {
        // This is a placeholder for the actual API call
        // When you have an actual API, replace this with a fetch call

        /*
        // Example of how the actual API call might look:
        const response = await fetch('https://api.hadith-classifier.com/classify', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ text: hadithText }),
        });

        if (!response.ok) {
          throw new Error(`API returned status code ${response.status}`);
        }

        const data = await response.json();
        setClassification(data);
        */

        // For now, we'll simulate a classification result
        const randomValue = Math.random();
        let simulatedClassification;

        if (randomValue < 0.25) {
          simulatedClassification = {
            status: 'Sahih',
            confidence: (0.85 + Math.random() * 0.15).toFixed(2),
            source: 'Simulation',
            details: 'This is a simulated classification. In a real implementation, this would be the result from an actual hadith classification API.'
          };
        } else if (randomValue < 0.5) {
          simulatedClassification = {
            status: 'Hasan',
            confidence: (0.65 + Math.random() * 0.20).toFixed(2),
            source: 'Simulation',
            details: 'This is a simulated classification. In a real implementation, this would be the result from an actual hadith classification API.'
          };
        } else if (randomValue < 0.75) {
          simulatedClassification = {
            status: 'Da\'if',
            confidence: (0.40 + Math.random() * 0.25).toFixed(2),
            source: 'Simulation',
            details: 'This is a simulated classification. In a real implementation, this would be the result from an actual hadith classification API.'
          };
        } else {
          simulatedClassification = {
            status: 'Mawdu',
            confidence: (0.10 + Math.random() * 0.30).toFixed(2),
            source: 'Simulation',
            details: 'This is a simulated classification. In a real implementation, this would be the result from an actual hadith classification API.'
          };
        }

        setClassification(simulatedClassification);
        setLoading(false);
      }, 1500); // Simulate a delay of 1.5 seconds
    } catch (error) {
      console.error('Error classifying hadith:', error);
      setError(`Failed to classify hadith: ${error.message}`);
      setLoading(false);
    }
  };

  // Function to get status color based on classification
  const getStatusColor = (status) => {
    switch (status) {
      case 'Sahih':
        return '#4CAF50'; // Green
      case 'Hasan':
        return '#8BC34A'; // Light Green
      case 'Da\'if':
        return '#FFC107'; // Amber
      case 'Mawdu':
        return '#F44336'; // Red
      default:
        return '#9E9E9E'; // Grey
    }
  };

  return (
    <div className="container" style={{ marginTop: '75px', position: 'relative' }}>

      <div style={{
        maxWidth: '800px',
        margin: '0 auto',
        padding: '30px',
        backgroundColor: '#f9f9f9',
        borderRadius: '10px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
      }}>
        <h1 style={{
          textAlign: 'center',
          marginBottom: '30px',
          color: '#8B4513',
          fontFamily: 'serif'
        }}>
          Hadith Classification
        </h1>

        <p style={{
          textAlign: 'center',
          marginBottom: '20px',
          color: '#666'
        }}>
          Enter a hadith text below to classify its authenticity
        </p>

        <div style={{ marginBottom: '20px' }}>
          <textarea
            placeholder="Enter hadith to classify"
            value={hadithText}
            onChange={(e) => setHadithText(e.target.value)}
            style={{
              width: '100%',
              minHeight: '150px',
              padding: '15px',
              borderRadius: '5px',
              border: '1px solid #d4af37',
              backgroundColor: '#FFF8E1',
              color: '#333',
              fontSize: '16px',
              fontFamily: 'Arial, sans-serif',
              resize: 'vertical'
            }}
          />
        </div>

        {error && (
          <div style={{
            color: 'red',
            marginBottom: '15px',
            textAlign: 'center',
            padding: '10px',
            backgroundColor: 'rgba(244, 67, 54, 0.1)',
            borderRadius: '5px'
          }}>
            {error}
          </div>
        )}

        <div style={{ textAlign: 'center', marginBottom: '30px' }}>
          <button
            onClick={classifyHadith}
            disabled={loading}
            style={{
              padding: '12px 30px',
              backgroundColor: '#8B4513',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              fontSize: '16px',
              cursor: loading ? 'not-allowed' : 'pointer',
              opacity: loading ? 0.7 : 1,
              transition: 'all 0.3s ease'
            }}
          >
            {loading ? 'Classifying...' : 'Classify'}
          </button>
        </div>

        {loading && (
          <div style={{ textAlign: 'center', marginBottom: '20px' }}>
            <p style={{ color: '#8B4513', fontStyle: 'italic' }}>
              Analyzing hadith...
            </p>
          </div>
        )}

        {classification && (
          <div style={{
            backgroundColor: '#fff',
            border: `2px solid ${getStatusColor(classification.status)}`,
            borderRadius: '8px',
            padding: '20px',
            marginTop: '20px'
          }}>
            <h3 style={{
              textAlign: 'center',
              color: getStatusColor(classification.status),
              marginBottom: '15px',
              fontFamily: 'serif'
            }}>
              Classification Result
            </h3>

            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              flexWrap: 'wrap',
              gap: '10px',
              marginBottom: '15px'
            }}>
              <div style={{
                flex: '1 1 200px',
                backgroundColor: 'rgba(212, 175, 55, 0.1)',
                padding: '15px',
                borderRadius: '5px',
                textAlign: 'center'
              }}>
                <h4 style={{ color: '#8B4513', marginBottom: '5px' }}>Status</h4>
                <p style={{
                  color: getStatusColor(classification.status),
                  fontSize: '20px',
                  fontWeight: 'bold',
                  margin: 0
                }}>
                  {classification.status}
                </p>
              </div>

              <div style={{
                flex: '1 1 200px',
                backgroundColor: 'rgba(212, 175, 55, 0.1)',
                padding: '15px',
                borderRadius: '5px',
                textAlign: 'center'
              }}>
                <h4 style={{ color: '#8B4513', marginBottom: '5px' }}>Confidence</h4>
                <p style={{
                  color: '#333',
                  fontSize: '20px',
                  fontWeight: 'bold',
                  margin: 0
                }}>
                  {(classification.confidence * 100).toFixed(0)}%
                </p>
              </div>

              <div style={{
                flex: '1 1 200px',
                backgroundColor: 'rgba(212, 175, 55, 0.1)',
                padding: '15px',
                borderRadius: '5px',
                textAlign: 'center'
              }}>
                <h4 style={{ color: '#8B4513', marginBottom: '5px' }}>Source</h4>
                <p style={{
                  color: '#333',
                  fontSize: '16px',
                  margin: 0
                }}>
                  {classification.source}
                </p>
              </div>
            </div>

            <div style={{
              backgroundColor: 'rgba(139, 69, 19, 0.05)',
              padding: '15px',
              borderRadius: '5px',
              marginTop: '15px'
            }}>
              <h4 style={{ color: '#8B4513', marginBottom: '10px' }}>Details</h4>
              <p style={{ color: '#555', margin: 0 }}>
                {classification.details}
              </p>
            </div>
          </div>
        )}

        <div style={{
          marginTop: '30px',
          padding: '15px',
          backgroundColor: 'rgba(212, 175, 55, 0.1)',
          borderRadius: '5px',
          textAlign: 'center'
        }}>
          <h4 style={{ color: '#8B4513', marginBottom: '10px' }}>About Hadith Classification</h4>
          <p style={{ color: '#555', margin: 0, fontSize: '14px' }}>
            Hadith classification is the process of determining the authenticity of a hadith based on its chain of narrators (isnad) and text (matn).
            The classifications include Sahih (authentic), Hasan (good), Da'if (weak), and Mawdu (fabricated).
          </p>
        </div>

        {/* Back to Home Button (Bottom) */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          marginTop: '30px'
        }}>
          <button
            onClick={() => navigate('/home')}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              background: '#d4af37',
              color: 'white',
              border: 'none',
              padding: '12px 20px',
              borderRadius: '5px',
              cursor: 'pointer',
              fontSize: '16px',
              boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
              transition: 'all 0.3s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
            }}
          >
            <FaArrowLeft /> Back to Home
          </button>
        </div>
      </div>
    </div>
  );
}

export default Hadith;
