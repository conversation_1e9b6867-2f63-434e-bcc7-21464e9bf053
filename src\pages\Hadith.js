import React, { useState } from 'react';
import { FaSearch, FaRandom } from 'react-icons/fa';
import { API_ENDPOINTS } from '../config/apiConfig';

function Hadith() {
  const [searchText, setSearchText] = useState('');
  const [hadithResults, setHadithResults] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchPerformed, setSearchPerformed] = useState(false);

  // Function to search hadiths
  const searchHadiths = async () => {
    if (!searchText.trim()) {
      setError('Please enter a word to search for hadiths');
      return;
    }

    setLoading(true);
    setError(null);
    setHadithResults('');
    setSearchPerformed(true);

    try {
      const response = await fetch(API_ENDPOINTS.SEARCH_HADITH(searchText.trim()), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to search hadiths: ${response.status}`);
      }

      const data = await response.json();
      console.log('📖 Search response:', data);

      if (data.success && data.data && Array.isArray(data.data) && data.data.length > 0) {
        // Combine all rawHtml from the hadith objects
        const combinedHtml = data.data
          .filter(hadith => hadith.rawHtml) // Filter out null/empty rawHtml
          .map(hadith => hadith.rawHtml)
          .join('<br/><br/>'); // Join with line breaks between hadiths

        if (combinedHtml) {
          setHadithResults(combinedHtml);
          console.log('✅ Successfully loaded', data.data.length, 'hadiths');
        } else {
          setError('No hadiths found for the search term');
        }
      } else {
        console.log('❌ Invalid response structure or no data');
        setError('No hadiths found for the search term');
      }
    } catch (error) {
      console.error('❌ Error searching hadiths:', error);
      setError(`Failed to search hadiths: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Function to get random hadith
  const getRandomHadith = async () => {
    setLoading(true);
    setError(null);
    setHadithResults('');
    setSearchPerformed(true);

    try {
      // Add timestamp to ensure we get a different random hadith each time
      const timestamp = new Date().getTime();
      const response = await fetch(`${API_ENDPOINTS.RANDOM_HADITH}?t=${timestamp}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get random hadith: ${response.status}`);
      }

      const data = await response.json();
      console.log('📖 Random hadith response:', data);

      if (data.rawHtml) {
        // For random hadith, display the single rawHtml directly
        setHadithResults(data.rawHtml);
        console.log('✅ Successfully loaded random hadith');
      } else if (data.success && data.data && Array.isArray(data.data) && data.data.length > 0) {
        // Fallback: if response structure is different, get first hadith
        const firstHadith = data.data.find(hadith => hadith.rawHtml);
        if (firstHadith && firstHadith.rawHtml) {
          setHadithResults(firstHadith.rawHtml);
          console.log('✅ Successfully loaded random hadith (fallback)');
        } else {
          setError('No random hadith found');
        }
      } else {
        console.log('❌ Invalid response structure or no data');
        setError('No random hadith found');
      }
    } catch (error) {
      console.error('❌ Error getting random hadith:', error);
      setError(`Failed to get random hadith: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Function to handle Enter key press
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      searchHadiths();
    }
  };

  return (
    <div className="container" style={{ marginTop: '75px', position: 'relative' }}>

      <div style={{
        maxWidth: '900px',
        margin: '0 auto',
        padding: '30px',
        backgroundColor: '#f9f9f9',
        borderRadius: '10px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
      }}>
        <h1 style={{
          textAlign: 'center',
          marginBottom: '30px',
          color: '#8B4513',
          fontFamily: 'serif'
        }}>
          Hadith Search
        </h1>

        <p style={{
          textAlign: 'center',
          marginBottom: '20px',
          color: '#666'
        }}>
          Search for hadiths by entering a word or get a random hadith
        </p>

        {/* Search Section */}
        <div style={{ marginBottom: '20px' }}>
          <div style={{ display: 'flex', gap: '10px', marginBottom: '15px' }}>
            <input
              type="text"
              placeholder="Enter a word to search for hadiths"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onKeyDown={handleKeyPress}
              style={{
                flex: 1,
                padding: '12px 15px',
                borderRadius: '5px',
                border: '1px solid #d4af37',
                backgroundColor: '#FFF8E1',
                color: '#333',
                fontSize: '16px',
                fontFamily: 'Arial, sans-serif'
              }}
            />
            <button
              onClick={searchHadiths}
              disabled={loading}
              style={{
                padding: '12px 20px',
                backgroundColor: '#8B4513',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                fontSize: '16px',
                cursor: loading ? 'not-allowed' : 'pointer',
                opacity: loading ? 0.7 : 1,
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              <FaSearch /> {loading ? 'Searching...' : 'Search'}
            </button>
          </div>

          {/* Random Hadith Button */}
          <div style={{ textAlign: 'center' }}>
            <button
              onClick={getRandomHadith}
              disabled={loading}
              style={{
                padding: '12px 25px',
                backgroundColor: '#d4af37',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                fontSize: '16px',
                cursor: loading ? 'not-allowed' : 'pointer',
                opacity: loading ? 0.7 : 1,
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                margin: '0 auto'
              }}
            >
              <FaRandom /> {loading ? 'Loading...' : 'Get Random Hadith'}
            </button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div style={{
            color: 'red',
            marginBottom: '15px',
            textAlign: 'center',
            padding: '15px',
            backgroundColor: 'rgba(244, 67, 54, 0.1)',
            borderRadius: '5px',
            border: '1px solid rgba(244, 67, 54, 0.3)'
          }}>
            {error}
          </div>
        )}

        {/* Loading Display */}
        {loading && (
          <div style={{ textAlign: 'center', marginBottom: '20px' }}>
            <p style={{ color: '#8B4513', fontStyle: 'italic', fontSize: '16px' }}>
              {searchPerformed ? 'Loading hadiths...' : 'Processing...'}
            </p>
          </div>
        )}

        {/* Hadith Results Display */}
        {hadithResults && !loading && (
          <div style={{
            backgroundColor: '#fff',
            border: '2px solid #d4af37',
            borderRadius: '8px',
            padding: '20px',
            marginTop: '20px',
            maxHeight: '600px',
            overflowY: 'auto'
          }}>
            <h3 style={{
              textAlign: 'center',
              color: '#8B4513',
              marginBottom: '20px',
              fontFamily: 'serif'
            }}>
              Hadith Results
            </h3>

            <div
              style={{
                color: '#333',
                lineHeight: '1.6',
                fontSize: '16px'
              }}
              dangerouslySetInnerHTML={{ __html: hadithResults }}
            />
          </div>
        )}


      </div>
    </div>
  );
}

export default Hadith;
