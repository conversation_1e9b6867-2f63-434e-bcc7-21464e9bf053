import { useState } from 'react';
import { createReadingPlan, saveReadingPlan, calculatePagesPerDay, calculateTotalDays } from '../utils/quranPlanUtils';
import { getStoredUserData, createReadingPlanOnServer } from '../utils/userApi';

function CompletionModal({ isOpen, onClose, onPlanCreated }) {
  const [duration, setDuration] = useState('');
  const [durationType, setDurationType] = useState('days');
  const [isCreating, setIsCreating] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!duration || duration <= 0) {
      alert('Please enter a valid duration');
      return;
    }

    setIsCreating(true);

    try {
      // Get current user data
      const userData = getStoredUserData();
      if (!userData || !userData.id) {
        alert('User not found. Please log in again.');
        setIsCreating(false);
        return;
      }

      // Convert months to days if necessary
      const daysForBackend = durationType === 'months' ? parseInt(duration) * 30 : parseInt(duration);

      console.log('🔄 Creating reading plan:', {
        userId: userData.id,
        duration: parseInt(duration),
        durationType,
        daysForBackend
      });

      // Create plan locally first
      const newPlan = createReadingPlan(parseInt(duration), durationType);

      // Save plan locally
      const saved = saveReadingPlan(newPlan);
      if (!saved) {
        alert('Failed to save reading plan locally. Please try again.');
        setIsCreating(false);
        return;
      }

      // Send request to backend server
      try {
        const serverResponse = await createReadingPlanOnServer(userData.id, daysForBackend);
        console.log('✅ Reading plan successfully created on server:', serverResponse);

        // Update local plan with task ID from server
        // The server returns an array of tasks, use the first task's ID
        let taskId = null;
        if (serverResponse) {
          if (Array.isArray(serverResponse) && serverResponse.length > 0) {
            taskId = serverResponse[0].id; // First task ID from array
            console.log('📋 Server returned array of tasks, using first task ID:', taskId);
          } else if (serverResponse.id) {
            taskId = serverResponse.id; // Single task object
            console.log('📋 Server returned single task, using task ID:', taskId);
          } else if (serverResponse.taskId) {
            taskId = serverResponse.taskId; // Alternative field name
            console.log('📋 Server returned taskId field:', taskId);
          }
        }

        if (taskId) {
          newPlan.taskId = taskId;
          saveReadingPlan(newPlan); // Save again with task ID
          console.log('✅ Local plan updated with task ID:', taskId);
        } else {
          console.log('⚠️ No task ID found in server response:', serverResponse);
        }
      } catch (serverError) {
        console.error('⚠️ Server request failed, but local plan was saved:', serverError);
        // Continue with local plan even if server fails
        // You might want to show a warning to the user here
      }

      // Success - notify parent component
      console.log('✅ Plan created successfully, notifying parent...');
      onPlanCreated(newPlan);
      // Let parent handle closing the modal
      setDuration('');

    } catch (error) {
      console.error('❌ Error creating reading plan:', error);
      alert('Failed to create reading plan. Please try again.');
    } finally {
      setIsCreating(false);
    }
  };

  const handleClose = () => {
    setDuration('');
    onClose();
  };

  const getPagesPerDay = () => {
    if (!duration || duration <= 0) return 0;
    return calculatePagesPerDay(parseInt(duration), durationType);
  };

  const getTotalDays = () => {
    if (!duration || duration <= 0) return 0;
    return calculateTotalDays(parseInt(duration), durationType);
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '15px',
        padding: '30px',
        maxWidth: '500px',
        width: '90%',
        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
        border: '2px solid #d4af37'
      }}>
        <h2 style={{
          textAlign: 'center',
          color: '#d4af37',
          marginBottom: '20px',
          fontSize: '24px',
          fontWeight: 'bold'
        }}>
          Create Quran Reading Plan
        </h2>
        
        <p style={{
          textAlign: 'center',
          color: '#666',
          marginBottom: '25px',
          fontSize: '16px'
        }}>
          Choose your preferred timeframe to complete reading all 604 pages of the Holy Quran
        </p>

        <form onSubmit={handleSubmit}>
          <div style={{ marginBottom: '20px' }}>
            <label style={{
              display: 'block',
              marginBottom: '8px',
              color: '#8B4513',
              fontWeight: 'bold',
              fontSize: '16px'
            }}>
              Duration:
            </label>
            <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
              <input
                type="number"
                value={duration}
                onChange={(e) => setDuration(e.target.value)}
                min="1"
                max={durationType === 'days' ? '365' : '12'}
                placeholder="Enter number"
                style={{
                  flex: 1,
                  padding: '12px',
                  borderRadius: '8px',
                  border: '2px solid #d4af37',
                  fontSize: '16px',
                  outline: 'none'
                }}
                required
              />
              <select
                value={durationType}
                onChange={(e) => setDurationType(e.target.value)}
                style={{
                  padding: '12px',
                  borderRadius: '8px',
                  border: '2px solid #d4af37',
                  fontSize: '16px',
                  backgroundColor: 'white',
                  cursor: 'pointer',
                  outline: 'none'
                }}
              >
                <option value="days">Days</option>
                <option value="months">Months</option>
              </select>
            </div>
          </div>

          {duration && duration > 0 && (
            <div style={{
              backgroundColor: '#FFF8E1',
              padding: '15px',
              borderRadius: '8px',
              marginBottom: '20px',
              border: '1px solid #d4af37'
            }}>
              <h4 style={{ color: '#8B4513', marginBottom: '10px', fontSize: '16px' }}>
                Plan Summary:
              </h4>
              <p style={{ color: '#666', margin: '5px 0', fontSize: '14px' }}>
                <strong>Total Duration:</strong> {getTotalDays()} days
              </p>
              <p style={{ color: '#666', margin: '5px 0', fontSize: '14px' }}>
                <strong>Pages per Day:</strong> {getPagesPerDay()} pages
              </p>
              <p style={{ color: '#666', margin: '5px 0', fontSize: '14px' }}>
                <strong>Reading Direction:</strong> From page 604 to page 1
              </p>
            </div>
          )}

          <div style={{
            display: 'flex',
            gap: '15px',
            justifyContent: 'center',
            marginTop: '25px'
          }}>
            <button
              type="button"
              onClick={handleClose}
              style={{
                padding: '12px 25px',
                borderRadius: '8px',
                border: '2px solid #ccc',
                backgroundColor: 'white',
                color: '#666',
                fontSize: '16px',
                cursor: 'pointer',
                fontWeight: 'bold',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = '#f5f5f5';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = 'white';
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isCreating || !duration || duration <= 0}
              style={{
                padding: '12px 25px',
                borderRadius: '8px',
                border: '2px solid #d4af37',
                backgroundColor: '#d4af37',
                color: 'white',
                fontSize: '16px',
                cursor: isCreating ? 'not-allowed' : 'pointer',
                fontWeight: 'bold',
                opacity: isCreating || !duration || duration <= 0 ? 0.6 : 1,
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                if (!isCreating && duration && duration > 0) {
                  e.target.style.backgroundColor = '#bfa131';
                }
              }}
              onMouseLeave={(e) => {
                if (!isCreating && duration && duration > 0) {
                  e.target.style.backgroundColor = '#d4af37';
                }
              }}
            >
              {isCreating ? 'Creating Plan...' : 'Create Plan'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default CompletionModal;
