import React, { useState, useEffect } from 'react';
import KaabaImg from '../assets/kaaba.png';
import MadinahImg from '../assets/madinah.png';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';

function Tafser() {
  // States for surahs list view
  const [surahs, setSurahs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // States for surah content view
  const [selectedSurah, setSelectedSurah] = useState(null);
  const [surahContent, setSurahContent] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedAyah, setSelectedAyah] = useState(null);
  const [tafsir, setTafsir] = useState(null);
  const [loadingTafsir, setLoadingTafsir] = useState(false);
  const [tafsirError, setTafsirError] = useState(null);

  // Tafsir selection state
  const [selectedTafseerId, setSelectedTafseerId] = useState(1); // Default to التفسير الميسر

  const AYAHS_PER_PAGE = 5; // Number of ayahs to show per page

  // Available tafsirs from quran-tafseer.com API
  const [availableTafseers, setAvailableTafseers] = useState([])

  // Fetch available tafseers
  useEffect(() => {
    async function fetchTafseers() {
      try {
        console.log('Fetching available tafseers from quran-tafseer.com API...');
        const response = await fetch('http://api.quran-tafseer.com/tafseer');

        if (!response.ok) {
          throw new Error(`API returned status code ${response.status}`);
        }

        const data = await response.json();
        console.log('Available tafseers from quran-tafseer.com:', data);

        if (data && Array.isArray(data)) {
          // Filter to only include Arabic tafseers
          const arabicTafseers = data.filter(tafseer => tafseer.language === 'ar');

          if (arabicTafseers.length > 0) {
            setAvailableTafseers(arabicTafseers);
            // Set default tafseer to the first Arabic one
            setSelectedTafseerId(arabicTafseers[0].id);
          } else {
            throw new Error('No Arabic tafseers found');
          }
        } else {
          throw new Error('Invalid data format received from API');
        }
      } catch (error) {
        console.error('Failed to fetch tafseers:', error);
        // Set default Arabic tafseers if API fails
        const defaultArabicTafseers = [
          { id: 1, name: "التفسير الميسر", language: "ar", author: "نخبة من العلماء", book_name: "التفسير الميسر" },
          { id: 4, name: "تفسير ابن كثير", language: "ar", author: "عماد الدين أبي الفداء إسماعيل بن كثير القرشي", book_name: "تفسير القرآن العظيم" },
          { id: 8, name: "تفسير الطبري", language: "ar", author: "الإمام أبو جعفر الطبري", book_name: "جامع البيان في تأويل القرآن" },
          { id: 7, name: "تفسير القرطبي", language: "ar", author: "أبو عبد الله محمد بن أحمد الأنصاري القرطبي", book_name: "الجامع لأحكام القرآن" }
        ];
        setAvailableTafseers(defaultArabicTafseers);
        setSelectedTafseerId(defaultArabicTafseers[0].id);
      }
    }

    fetchTafseers();
  }, []);

  // Fetch surahs list
  useEffect(() => {
    async function fetchSurahs() {
      try {
        setLoading(true);
        setError(null);
        console.log('Fetching surahs data from Alquran.cloud API...');

        const response = await fetch('https://api.alquran.cloud/v1/surah');

        if (!response.ok) {
          throw new Error(`API returned status code ${response.status}`);
        }

        const data = await response.json();
        console.log('Surah data response:', data);

        if (!data || !data.data || data.code !== 200) {
          throw new Error('Invalid data format received from API');
        }

        const formattedSurahs = data.data.map(surah => ({
          number: surah.number,
          englishName: surah.englishName,
          arabicName: surah.name,
          verses: surah.numberOfAyahs,
          revelationType: surah.revelationType,
        }));

        console.log('Formatted surahs:', formattedSurahs);
        setSurahs(formattedSurahs);
      } catch (error) {
        console.error('API fetch failed:', error);
        setError(`Failed to fetch Quran data: ${error.message}`);
      } finally {
        setLoading(false);
      }
    }

    fetchSurahs();
  }, []);

  // Fetch surah content when a surah is selected
  useEffect(() => {
    if (!selectedSurah) return;

    async function fetchSurahContent() {
      try {
        setLoading(true);
        setError(null);
        console.log(`Fetching surah ${selectedSurah} data from Alquran.cloud API...`);

        // Fetch Arabic text
        const arabicResponse = await fetch(`https://api.alquran.cloud/v1/surah/${selectedSurah}`);

        if (!arabicResponse.ok) {
          throw new Error(`API returned status code ${arabicResponse.status}`);
        }

        const arabicData = await arabicResponse.json();
        console.log('Arabic surah data:', arabicData);

        if (!arabicData || !arabicData.data || arabicData.code !== 200) {
          throw new Error('Invalid data format received from API for Arabic text');
        }

        // Fetch English translation
        const englishResponse = await fetch(`https://api.alquran.cloud/v1/surah/${selectedSurah}/en.asad`);

        if (!englishResponse.ok) {
          throw new Error(`API returned status code ${englishResponse.status} for English translation`);
        }

        const englishData = await englishResponse.json();
        console.log('English surah data:', englishData);

        if (!englishData || !englishData.data || englishData.code !== 200) {
          throw new Error('Invalid data format received from API for English translation');
        }

        // Combine the Arabic and English data with correct ayah numbering (starting from 1 for each surah)
        const combinedSurah = {
          number: arabicData.data.number,
          englishName: arabicData.data.englishName,
          arabicName: arabicData.data.name,
          ayahs: arabicData.data.ayahs.map((ayah, index) => ({
            number: index + 1, // Start numbering from 1 for each surah
            globalNumber: ayah.number, // Keep the global number for API calls
            surahNumber: selectedSurah,
            arabicText: ayah.text,
            englishText: englishData.data.ayahs[index].text
          }))
        };

        console.log('Combined surah data:', combinedSurah);
        setSurahContent(combinedSurah);
        setCurrentPage(1);
        setSelectedAyah(null);
        setTafsir(null);
      } catch (error) {
        console.error('Error fetching surah:', error);
        setError(`Failed to fetch Quran data: ${error.message}`);
      } finally {
        setLoading(false);
      }
    }

    fetchSurahContent();
  }, [selectedSurah]);

  // Function to fetch tafsir for a specific ayah
  const fetchTafsir = async (surahNumber, ayahNumber) => {
    try {
      setLoadingTafsir(true);
      setTafsirError(null);

      console.log(`Fetching tafsir for Surah ${surahNumber}, Ayah ${ayahNumber} with tafsir ID ${selectedTafseerId}...`);

      // Fetch tafsir from quran-tafseer.com API
      const response = await fetch(`http://api.quran-tafseer.com/tafseer/${selectedTafseerId}/${surahNumber}/${ayahNumber}`);

      if (!response.ok) {
        throw new Error(`API returned status code ${response.status}`);
      }

      const data = await response.json();
      console.log('Tafsir data from quran-tafseer.com API:', data);

      // Find the tafsir info
      const tafsirInfo = availableTafseers.find(t => t.id === selectedTafseerId);

      // Get the tafsir text
      const tafsirText = data?.text || '';

      if (tafsirText) {
        setTafsir({
          surahNumber,
          ayahNumber,
          tafsirId: selectedTafseerId,
          tafsirName: tafsirInfo ? tafsirInfo.name : 'تفسير',
          authorName: tafsirInfo ? tafsirInfo.author : '',
          arabicText: tafsirText
        });
      } else {
        // fallback message if no text
        setTafsir({
          surahNumber,
          ayahNumber,
          tafsirId: selectedTafseerId,
          tafsirName: tafsirInfo ? tafsirInfo.name : 'تفسير',
          authorName: tafsirInfo ? tafsirInfo.author : '',
          arabicText: "هذه الآية مشمولة في التفسير، ولكن النص المحدد غير متوفر من خلال واجهة برمجة التطبيقات. يرجى الرجوع إلى مصدر تفسير كامل."
        });
      }
    } catch (error) {
      console.error('Error fetching tafsir:', error);
      setTafsirError(`Failed to fetch tafsir: ${error.message}`);

      // fallback to translation
      try {
        const response = await fetch(`https://api.alquran.cloud/v1/ayah/${surahNumber}:${ayahNumber}`);

        if (response.ok) {
          const data = await response.json();

          if (data?.data && data.code === 200) {
            setTafsir({
              surahNumber,
              ayahNumber,
              tafsirId: 'translation',
              tafsirName: 'ترجمة',
              authorName: '',
              arabicText: `<p>${data.data.text}</p><p><em>لم يتم العثور على تفسير لهذه الآية.</em></p>`
            });
          }
        }
      } catch (fallbackError) {
        console.error('Even fallback translation failed:', fallbackError);
      }
    } finally {
      setLoadingTafsir(false);
    }
  };

  // Handle selecting an ayah for tafsir
  const handleAyahSelect = (ayahNumber) => {
    if (selectedAyah === ayahNumber) {
      // If the same ayah is clicked again, toggle it off
      setSelectedAyah(null);
      setTafsir(null);
    } else {
      // Otherwise, select the ayah and fetch its tafsir
      setSelectedAyah(ayahNumber);
      setTafsir(null);  // clear old tafsir
      fetchTafsir(selectedSurah, ayahNumber);

    }
  };

  // Handle going back to surahs list
  const handleBackToSurahs = () => {
    setSelectedSurah(null);
    setSurahContent(null);
    setSelectedAyah(null);
    setTafsir(null);
  };

  // Calculate pagination for surah content
  const calculatePagination = () => {
    if (!surahContent) return { totalPages: 0, startIndex: 0, endIndex: 0, currentAyahs: [] };

    const totalPages = Math.ceil(surahContent.ayahs.length / AYAHS_PER_PAGE);
    const startIndex = (currentPage - 1) * AYAHS_PER_PAGE;
    const endIndex = Math.min(startIndex + AYAHS_PER_PAGE, surahContent.ayahs.length);
    const currentAyahs = surahContent.ayahs.slice(startIndex, endIndex);

    return { totalPages, startIndex, endIndex, currentAyahs };
  };

  // Handle page navigation
  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
      window.scrollTo(0, 0);
    }
  };

  const goToNextPage = () => {
    const { totalPages } = calculatePagination();
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
      window.scrollTo(0, 0);
    }
  };

  // Handle surah navigation
  const goToPreviousSurah = () => {
    if (selectedSurah > 1) {
      setSelectedSurah(selectedSurah - 1);
    }
  };

  const goToNextSurah = () => {
    if (selectedSurah < 114) {
      setSelectedSurah(selectedSurah + 1);
    }
  };

  // Render surahs list view
  const renderSurahsList = () => {
    return (
      <div style={{ padding: '30px', marginTop: '80px', backgroundColor: '#f9f9f9' }}>
        <h1 style={{ textAlign: 'center', marginBottom: '30px', color: '#bfa131' }}>
          Quran Tafsir
        </h1>

        {loading && (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <p style={{ fontSize: '18px', color: '#bfa131' }}>Loading Quran surahs...</p>
          </div>
        )}

        {error && (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <p style={{ fontSize: '18px', color: 'red' }}>{error}</p>
            <button
              onClick={() => window.location.reload()}
              style={{
                marginTop: '20px',
                padding: '10px 20px',
                backgroundColor: '#bfa131',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                cursor: 'pointer'
              }}
            >
              Try Again
            </button>
          </div>
        )}

        {!loading && !error && surahs.length === 0 && (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <p style={{ fontSize: '18px', color: '#bfa131' }}>No surahs found. Please try again later.</p>
          </div>
        )}

        {!loading && !error && surahs.length > 0 && (
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
            {surahs.map(surah => (
              <div key={surah.number}
                onClick={() => setSelectedSurah(surah.number)}
                style={{
                  backgroundColor: '#f5f5f5',
                  padding: '10px 20px',
                  borderRadius: '15px',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
                  textAlign: 'center',
                  height: '100px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  transition: 'all 0.3s ease',
                  cursor: 'pointer',
                }}
                onMouseEnter={e => e.currentTarget.style.transform = 'scale(1.03)'}
                onMouseLeave={e => e.currentTarget.style.transform = 'scale(1)'}>

                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <img
                    src={surah.revelationType === 'Meccan' ? KaabaImg : MadinahImg}
                    alt={surah.revelationType}
                    style={{ width: '55px', height: '55px', marginRight: '10px', marginTop: '10px' }}
                  />
                  <div style={{ flex: 1, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <h4 style={{ margin: 0, color: '#bfa131', fontSize: '20px' }}>
                      {surah.number}. {surah.englishName}
                    </h4>
                    <h4 style={{ margin: 0, color: '#d4af37', fontSize: '22px', textAlign: 'right' }}>
                      {surah.arabicName}
                    </h4>
                  </div>
                </div>

                <p style={{ margin: 0, fontSize: '14px', color: '#555', marginTop: '-10px' }}>
                  {surah.verses} ayahs
                </p>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  // Render surah content view with tafsir
  const renderSurahContent = () => {
    if (!surahContent) return null;

    const { totalPages, startIndex, endIndex, currentAyahs } = calculatePagination();

    return (
      <div style={{
        marginTop: '100px',
        padding: '20px',
        maxWidth: '1000px',
        margin: '100px auto',
        position: 'relative'
      }}>
        {/* Back button */}
        <button
          onClick={handleBackToSurahs}
          style={{
            position: 'absolute',
            top: '140px',
            left: '29px',
            background: '#8B4513',
            color: 'white',
            border: 'none',
            padding: '8px 15px',
            borderRadius: '5px',
            cursor: 'pointer'
          }}    onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
          }}
        >
          Back to Surahs
        </button>

        {/* Quran Book Header */}
        <div style={{
          textAlign: 'center',
          marginBottom: '30px',
          borderBottom: '2px solid #d4af37',
          paddingBottom: '15px'
        }}>
          <h1 style={{
            color: '#d4af37',
            marginBottom: '10px',
            fontFamily: 'serif'
          }}>
            {surahContent.englishName} - Tafsir
          </h1>
          <h2 style={{
            color: '#8B4513',
            fontFamily: 'serif',
            fontSize: '32px'
          }}>
            {surahContent.arabicName}
          </h2>

          {/* Tafsir Selection Dropdown */}
          <div style={{
            margin: '15px auto',
            maxWidth: '300px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center'
          }}>
            <label
              htmlFor="tafsir-select"
              style={{
                marginBottom: '5px',
                color: '#8B4513',
                fontWeight: 'bold',
                fontSize: '16px'
              }}
            >
              اختر التفسير:
            </label>
            <select
              id="tafsir-select"
              value={selectedTafseerId}
              onChange={(e) => {
                setSelectedTafseerId(Number(e.target.value));
                // Clear current tafsir when changing source
                if (selectedAyah) {
                  setTafsir(null);
                  fetchTafsir(selectedSurah, selectedAyah);
                }
              }}
              style={{
                padding: '8px 12px',
                borderRadius: '5px',
                border: '1px solid #d4af37',
                backgroundColor: '#FFF8E1',
                color: '#8B4513',
                fontSize: '16px',
                width: '100%',
                cursor: 'pointer',
                direction: 'rtl'
              }}
            >
              {availableTafseers.map(tafseer => (
                <option key={tafseer.id} value={tafseer.id}>
                  {tafseer.name} - {tafseer.author}
                </option>
              ))}
            </select>
          </div>

          <p style={{
            color: '#8B4513',
            fontStyle: 'italic',
            marginTop: '15px'
          }}>
            Page {currentPage} of {totalPages} • Ayahs {startIndex + 1}-{endIndex} of {surahContent.ayahs.length}
          </p>
        </div>

        {/* Quran Book Pages with Side Navigation */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          position: 'relative',
          minHeight: '600px',
        }}>
          {/* Left Navigation Button (Next Page for RTL) */}
          <button
            onClick={goToNextPage}
            disabled={currentPage >= totalPages}
            style={{
              background: 'transparent',
              color: currentPage >= totalPages ? '#ccc' : '#8B4513',
              border: 'none',
              fontSize: '24px',
              cursor: currentPage >= totalPages ? 'not-allowed' : 'pointer',
              padding: '10px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 10,
              position: 'absolute',
              left: '-20px',
              top: '50%',
              transform: 'translateY(-50%)',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
              backgroundColor: 'white'
            }}
          >
            <FaChevronLeft />
          </button>

          {/* Quran Content */}
          <div style={{
            background: '#FFF8E1',
            border: '1px solid #d4af37',
            borderRadius: '5px',
            boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)',
            padding: '40px',
            position: 'relative',
            width: '100%',
            minHeight: '600px',
            backgroundImage: 'linear-gradient(to right, rgba(212, 175, 55, 0.05) 1px, transparent 1px)',
            backgroundSize: '25px 100%'
          }}>
            {/* Decorative Quran Page Border */}
            <div style={{
              position: 'absolute',
              top: '10px',
              left: '10px',
              right: '10px',
              bottom: '10px',
              border: '2px solid rgba(139, 69, 19, 0.2)',
              pointerEvents: 'none'
            }}></div>

            {/* Bismillah at the top of each page except for Surah 9 */}
            {surahContent.number !== 9 && currentPage === 1 && (
              <div style={{
                textAlign: 'center',
                marginBottom: '30px',
                fontFamily: 'serif',
                fontSize: '24px',
                color: '#8B4513'
              }}>
                بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ
              </div>
            )}

            {/* Ayahs */}
            <div style={{ direction: 'rtl' }}>
              {currentAyahs.map((ayah, index) => (
                <div key={index} style={{
                  marginBottom: '25px',
                  position: 'relative',
                  paddingBottom: '15px',
                  borderBottom: selectedAyah === ayah.number ? '2px solid #d4af37' : '1px solid rgba(139, 69, 19, 0.2)'
                }}>
                  {/* Arabic Text */}
                  <p style={{
                    textAlign: 'right',
                    fontSize: '26px',
                    lineHeight: '2',
                    fontFamily: 'serif',
                    color: '#000',
                    marginBottom: '15px'
                  }}>
                    {ayah.arabicText}
                    <span style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '35px',
                      height: '35px',
                      borderRadius: '50%',
                      background: selectedAyah === ayah.number ? '#8B4513' : '#d4af37',
                      color: 'white',
                      fontSize: '14px',
                      marginRight: '10px'
                    }}>
                      {ayah.number}
                    </span>
                  </p>

                  {/* English Translation */}
                  <p style={{
                    fontSize: '16px',
                    color: '#555',
                    fontFamily: 'serif',
                    lineHeight: '1.6',
                    direction: 'ltr',
                    textAlign: 'left',
                    borderTop: '1px solid rgba(139, 69, 19, 0.2)',
                    paddingTop: '10px'
                  }}>
                    {ayah.englishText}
                  </p>

                  {/* Get Tafsir Button */}
                  <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    marginTop: '10px'
                  }}>
                    <button
                      onClick={() => handleAyahSelect(ayah.number)}
                      style={{
                        background: selectedAyah === ayah.number ? '#8B4513' : '#d4af37',
                        color: 'white',
                        border: 'none',
                        padding: '5px 15px',
                        borderRadius: '5px',
                        cursor: 'pointer',
                        fontSize: '14px'
                      }}
                    >
                      {selectedAyah === ayah.number ? 'Hide Tafsir' : 'Show Tafsir'}
                    </button>
                  </div>

                  {/* Tafsir Content */}
                  {selectedAyah === ayah.number && tafsir && tafsir.ayahNumber === ayah.number && (
                    <div style={{
                      marginTop: '15px',
                      padding: '15px',
                      backgroundColor: 'rgba(212, 175, 55, 0.1)',
                      borderRadius: '5px',
                      direction: 'ltr'
                    }}>
                      <h4 style={{ color: '#8B4513', marginBottom: '5px' }}>
                        {tafsir && tafsir.tafsirName ?
                          `${tafsir.tafsirName} for Ayah ${ayah.number}:` :
                          `Tafsir for Ayah ${ayah.number}:`
                        }
                      </h4>

                      {tafsir && tafsir.authorName && (
                        <p style={{ color: '#8B4513', marginTop: '0', marginBottom: '10px', fontSize: '14px', fontStyle: 'italic' }}>
                          by {tafsir.authorName}
                        </p>
                      )}

                      {loadingTafsir && (
                        <p style={{ color: '#8B4513', fontStyle: 'italic' }}>
                          Loading tafsir for Ayah {ayah.number}...
                        </p>
                      )}

                      {tafsirError && (
                        <p style={{ color: 'red' }}>{tafsirError}</p>
                      )}

                      {selectedAyah === ayah.number && tafsir && tafsir.surahNumber === selectedSurah && tafsir.ayahNumber === ayah.number && (
                        <div>
                          {/* Arabic Tafsir Section */}
                          {tafsir.arabicText && (
                            <div style={{
                              marginBottom: '20px',
                              padding: '15px',
                              backgroundColor: 'rgba(139, 69, 19, 0.05)',
                              borderRadius: '5px',
                              direction: 'rtl',
                              textAlign: 'right'
                            }}>
                              <h5 style={{
                                color: '#8B4513',
                                marginBottom: '10px',
                                fontFamily: 'serif',
                                fontSize: '18px',
                                textAlign: 'right'
                              }}>
                                {tafsir.tafsirName && tafsir.authorName ?
                                  `${tafsir.tafsirName} - ${tafsir.authorName}` :
                                  'التفسير:'}
                              </h5>
                              <div
                                style={{
                                  fontSize: '18px',
                                  lineHeight: '1.8',
                                  fontFamily: 'serif',
                                  color: '#333'
                                }}
                                dangerouslySetInnerHTML={{ __html: tafsir.arabicText }}
                              />
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Right Navigation Button (Previous Page for RTL) */}
          <button
            onClick={goToPreviousPage}
            disabled={currentPage <= 1}
            style={{
              background: 'transparent',
              color: currentPage <= 1 ? '#ccc' : '#8B4513',
              border: 'none',
              fontSize: '24px',
              cursor: currentPage <= 1 ? 'not-allowed' : 'pointer',
              padding: '10px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 10,
              position: 'absolute',
              right: '-20px',
              top: '50%',
              transform: 'translateY(-50%)',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
              backgroundColor: 'white'
            }}
          >
            <FaChevronRight />
          </button>
        </div>

        {/* Surah Navigation */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginTop: '30px',
          alignItems: 'center'
        }}>
          {/* Next Surah Button (Left side for RTL) */}
          <button
            onClick={goToNextSurah}
            disabled={selectedSurah >= 114}
            style={{
              background: selectedSurah >= 114 ? '#ccc' : '#8B4513',
              color: 'white',
              border: 'none',
              padding: '10px 15px',
              borderRadius: '5px',
              cursor: selectedSurah >= 114 ? 'not-allowed' : 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '5px'
            }}
          >
            <FaChevronLeft /> Next Surah
          </button>

          {/* Page Information */}
          <div style={{
            color: '#8B4513',
            fontStyle: 'italic',
            fontSize: '14px'
          }}>
            Page {currentPage} of {totalPages} • Ayahs {startIndex + 1}-{endIndex} of {surahContent.ayahs.length}
          </div>

          {/* Previous Surah Button (Right side for RTL) */}
          <button
            onClick={goToPreviousSurah}
            disabled={selectedSurah <= 1}
            style={{
              background: selectedSurah <= 1 ? '#ccc' : '#8B4513',
              color: 'white',
              border: 'none',
              padding: '10px 15px',
              borderRadius: '5px',
              cursor: selectedSurah <= 1 ? 'not-allowed' : 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '5px'
            }}
          >
            Previous Surah <FaChevronRight />
          </button>
        </div>
      </div>
    );
  };

  return (
    <div>
      {selectedSurah ? renderSurahContent() : renderSurahsList()}
    </div>
  );
}

export default Tafser;
