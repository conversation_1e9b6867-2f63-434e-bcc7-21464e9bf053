// Notification Service Manager
class NotificationService {
  constructor() {
    this.serviceWorker = null;
    this.isServiceWorkerReady = false;
    this.messageHandlers = new Map();
  }

  // Initialize the service worker
  async init() {
    if ('serviceWorker' in navigator) {
      try {
        // Register the service worker
        const registration = await navigator.serviceWorker.register('/duaa-service-worker.js', {
          scope: '/'
        });

        console.log('Duaa Service Worker registered:', registration);

        // Wait for the service worker to be ready
        await navigator.serviceWorker.ready;
        this.isServiceWorkerReady = true;

        // Set up message listener
        navigator.serviceWorker.addEventListener('message', (event) => {
          this.handleServiceWorkerMessage(event);
        });

        // Get the active service worker
        this.serviceWorker = registration.active || registration.waiting || registration.installing;

        // Listen for service worker state changes
        if (this.serviceWorker) {
          this.serviceWorker.addEventListener('statechange', () => {
            if (this.serviceWorker.state === 'activated') {
              this.isServiceWorkerReady = true;
            }
          });
        }

        return registration;
      } catch (error) {
        console.error('Service Worker registration failed:', error);
        throw error;
      }
    } else {
      throw new Error('Service Workers not supported');
    }
  }

  // Handle messages from service worker
  handleServiceWorkerMessage(event) {
    const { type, data } = event.data;
    
    switch (type) {
      case 'REQUEST_NOTIFICATION_STATUS':
        this.sendNotificationStatus();
        break;
      case 'DUAA_NOTIFICATIONS_STARTED':
        console.log('Duaa notifications started in service worker');
        break;
      case 'DUAA_NOTIFICATIONS_STOPPED':
        console.log('Duaa notifications stopped in service worker');
        break;
      default:
        // Handle custom message handlers
        if (this.messageHandlers.has(type)) {
          this.messageHandlers.get(type)(data);
        }
    }
  }

  // Send message to service worker with proper response handling
  async sendMessage(type, data = null) {
    if (!this.isServiceWorkerReady) {
      console.warn('Service worker not ready, queuing message:', type);
      // Wait a bit and try again
      await new Promise(resolve => setTimeout(resolve, 1000));
      return this.sendMessage(type, data);
    }

    if (navigator.serviceWorker.controller) {
      return new Promise((resolve, reject) => {
        const messageChannel = new MessageChannel();

        messageChannel.port1.onmessage = (event) => {
          if (event.data.success) {
            resolve(event.data);
          } else {
            reject(new Error(event.data.error || 'Service worker operation failed'));
          }
        };

        // Send message with response port
        navigator.serviceWorker.controller.postMessage(
          { type, data },
          [messageChannel.port2]
        );

        // Set timeout to prevent hanging
        setTimeout(() => {
          reject(new Error('Service worker response timeout'));
        }, 10000);
      });
    } else {
      throw new Error('No service worker controller available');
    }
  }

  // Test system notification directly (for immediate testing)
  async testSystemNotification() {
    console.log('🧪 Testing direct system notification...');

    // Check permission first
    if (Notification.permission !== 'granted') {
      console.error('❌ Notification permission not granted');
      throw new Error('Notification permission not granted');
    }

    try {
      // Use predefined Duaa collection to avoid CORS issues
      const fallbackDuas = [
        'اللَّهُمَّ أَعِنِّي عَلَى ذِكْرِكَ وَشُكْرِكَ وَحُسْنِ عِبَادَتِكَ\n\n(O Allah, help me to remember You, thank You, and worship You in the best manner)',
        'رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً وَفِي الْآخِرَةِ حَسَنَةً وَقِنَا عَذَابَ النَّارِ\n\n(Our Lord, give us good in this world and good in the next world, and save us from the punishment of the Fire)',
        'اللَّهُمَّ اغْفِرْ لِي ذَنْبِي وَوَسِّعْ لِي فِي دَارِي وَبَارِكْ لِي فِي رِزْقِي\n\n(O Allah, forgive my sins, expand my home for me, and bless my sustenance)',
        'اللَّهُمَّ إِنِّي أَسْأَلُكَ الْهُدَى وَالتُّقَى وَالْعَفَافَ وَالْغِنَى\n\n(O Allah, I ask You for guidance, piety, chastity and contentment)'
      ];

      // Select random Duaa
      const randomIndex = Math.floor(Math.random() * fallbackDuas.length);
      const duaaText = fallbackDuas[randomIndex];

      console.log('🎲 Selected Duaa for test notification:', duaaText);

      // Create direct system notification
      const notification = new Notification('🤲 Duaa Reminder', {
        body: duaaText,
        icon: '/favicon.ico',
        tag: 'duaa-test',
        requireInteraction: false,
        silent: false,
        dir: 'auto',
        lang: 'ar'
      });

      console.log('✅ Direct system notification created successfully!');

      // Handle notification click
      notification.onclick = () => {
        window.focus();
        notification.close();
      };

      return notification;

    } catch (error) {
      console.error('❌ Error creating direct notification:', error);
      throw error;
    }
  }

  // Start Duaa notifications (every 5 minutes)
  async startDuaaNotifications() {
    console.log('🔔 NotificationService: Starting Duaa notifications (every 5 minutes)...');

    // Check permission first
    if (Notification.permission !== 'granted') {
      console.error('❌ Notification permission not granted');
      throw new Error('Notification permission not granted');
    }

    try {
      const response = await this.sendMessage('START_DUAA_NOTIFICATIONS');
      console.log('✅ NotificationService: Start message sent to service worker for 5-minute intervals', response);
      return response;
    } catch (error) {
      console.error('❌ Failed to start Duaa notifications:', error);
      throw error;
    }
  }

  // Stop Duaa notifications
  async stopDuaaNotifications() {
    console.log('🛑 NotificationService: Stopping Duaa notifications...');
    try {
      const response = await this.sendMessage('STOP_DUAA_NOTIFICATIONS');
      console.log('✅ NotificationService: Stop message sent to service worker', response);
      return response;
    } catch (error) {
      console.error('❌ Failed to stop Duaa notifications:', error);
      throw error;
    }
  }

  // Send current notification status to service worker
  sendNotificationStatus() {
    try {
      const settings = localStorage.getItem('notificationSettings');
      if (settings) {
        const parsedSettings = JSON.parse(settings);
        const shouldStart = parsedSettings.enabled && parsedSettings.duaa;
        
        if (shouldStart) {
          this.startDuaaNotifications();
        } else {
          this.stopDuaaNotifications();
        }
      }
    } catch (error) {
      console.error('Error sending notification status:', error);
    }
  }

  // Check if notifications are supported
  static isSupported() {
    return 'serviceWorker' in navigator && 'Notification' in window;
  }

  // Request notification permission
  static async requestPermission() {
    if (!('Notification' in window)) {
      throw new Error('Notifications not supported');
    }

    const permission = await Notification.requestPermission();
    return permission;
  }

  // Get current notification permission
  static getPermission() {
    if ('Notification' in window) {
      return Notification.permission;
    }
    return 'default';
  }

  // Add custom message handler
  addMessageHandler(type, handler) {
    this.messageHandlers.set(type, handler);
  }

  // Remove custom message handler
  removeMessageHandler(type) {
    this.messageHandlers.delete(type);
  }

  // Unregister service worker (for cleanup)
  async unregister() {
    if ('serviceWorker' in navigator) {
      const registrations = await navigator.serviceWorker.getRegistrations();
      for (const registration of registrations) {
        if (registration.scope.includes('duaa-service-worker')) {
          await registration.unregister();
          console.log('Duaa Service Worker unregistered');
        }
      }
    }
  }
}

// Create singleton instance
const notificationService = new NotificationService();

// Auto-initialize when module is loaded
if (typeof window !== 'undefined') {
  // Initialize service worker when the page loads
  window.addEventListener('load', async () => {
    try {
      await notificationService.init();
      console.log('✅ Notification service initialized');

      // Log current notification permission status
      console.log('🔐 Current notification permission:', Notification.permission);

      // Add global test function for debugging
      window.testNotification = async () => {
        try {
          console.log('🧪 Global notification test...');
          if (Notification.permission !== 'granted') {
            const permission = await Notification.requestPermission();
            console.log('📱 Permission result:', permission);
          }

          if (Notification.permission === 'granted') {
            const notification = new Notification('🧪 Test Notification', {
              body: 'This is a test system notification from the Quran website.',
              icon: '/favicon.ico',
              tag: 'test-notification'
            });

            notification.onclick = () => {
              console.log('📱 Notification clicked');
              window.focus();
              notification.close();
            };

            console.log('✅ Test notification created successfully!');
            return notification;
          } else {
            throw new Error('Notification permission denied');
          }
        } catch (error) {
          console.error('❌ Test notification failed:', error);
          throw error;
        }
      };

      console.log('🛠️ Global test function added: window.testNotification()');

    } catch (error) {
      console.error('❌ Failed to initialize notification service:', error);
    }
  });
}

export default notificationService;
