import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Bsg from '../assets/bsg.jpg';
import QuranImage from '../assets/Quran.png';
import VisionIcon from '../assets/vision.png';
import MissionIcon from '../assets/mission.png';
import ShahadahImg from '../assets/shahadah.png';
import SalahImg from '../assets/salah.png';
import SawmImg from '../assets/sawm.png';
import ZakatImg from '../assets/zakat.png';
import HajjImg from '../assets/hajj.png';
import CompletionModal from '../components/CompletionModal';
import CompletionView from '../components/CompletionView';
import { loadReadingPlan, getCurrentReadingTask, calculateCompletionPercentage } from '../utils/quranPlanUtils';
import { initializeUserData, getStoredUserData, syncUserDataAfterLogin } from '../utils/userApi';
import { API_ENDPOINTS } from '../config/apiConfig';
import { SURAH_PAGE_RANGES } from '../utils/surahPageMapping';

function Home() {
  const navigate = useNavigate();
  const [userData, setUserData] = useState(null);
  const [prayerTimes, setPrayerTimes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showFullText, setShowFullText] = useState(false);

  // Completion feature states
  const [readingPlan, setReadingPlan] = useState(null);
  const [showCompletionModal, setShowCompletionModal] = useState(false);
  const [showCompletionView, setShowCompletionView] = useState(false);
  const [currentReadingTask, setCurrentReadingTask] = useState(null);
  const [lastRead, setLastRead] = useState({ surahName: 'Al-Fatihah', lastPage: 1 });
  const [streakData, setStreakData] = useState({ currentStreak: 0, maxStreak: 0 });

  useEffect(() => {
    async function fetchUserData() {
      try {
        // Initialize user data (fetch from API if needed)
        const userData = await initializeUserData();
        if (userData) {
          console.log('User data loaded:', userData);
          setUserData(userData);
        } else {
          console.error('No user data found');
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
        // Fallback to stored data
        const storedData = getStoredUserData();
        if (storedData) {
          setUserData(storedData);
        }
      }
    }

    fetchUserData();
  }, []);

  useEffect(() => {
    async function fetchPrayerTimes() {
      try {
        const response = await fetch(
          'https://api.aladhan.com/v1/timingsByCity?city=Cairo&country=Egypt&method=5'
        );
        const data = await response.json();
        const timings = data.data.timings;

        const prayers = [
          { name: 'Fajr', time: timings.Fajr },
          { name: 'Dhuhr', time: timings.Dhuhr },
          { name: 'Asr', time: timings.Asr },
          { name: 'Maghrib', time: timings.Maghrib },
          { name: 'Isha', time: timings.Isha },
          { name: 'Jummah', time: timings.Dhuhr },
        ];

        setPrayerTimes(prayers);
        setLoading(false);
      } catch (error) {
        console.error('Failed to fetch prayer times:', error);
        setLoading(false);
      }
    }

    fetchPrayerTimes();
  }, []);

  // Load reading plan on component mount and when user data changes
  useEffect(() => {
    const plan = loadReadingPlan();
    if (plan) {
      setReadingPlan(plan);
      const task = getCurrentReadingTask(plan);
      setCurrentReadingTask(task);
    }
  }, []);

  // Sync with server when user data is available (after login)
  useEffect(() => {
    if (userData && userData.id) {
      console.log('🔄 User logged in, syncing with server...');

      const syncData = async () => {
        try {
          // Sync server data with local data
          const syncedPlan = await syncUserDataAfterLogin(userData.id);

          if (syncedPlan) {
            setReadingPlan(syncedPlan);
            const task = getCurrentReadingTask(syncedPlan);
            setCurrentReadingTask(task);
            console.log('✅ Reading plan synced with server:', syncedPlan);
          } else {
            // No server plan, check local
            const localPlan = loadReadingPlan();
            if (localPlan) {
              setReadingPlan(localPlan);
              const task = getCurrentReadingTask(localPlan);
              setCurrentReadingTask(task);
              console.log('✅ Local reading plan loaded:', localPlan);
            } else {
              console.log('📝 No reading plan found locally or on server');
            }
          }

          // Fetch last read data and streak data
          await fetchLastReadData();
          await fetchStreakData();
        } catch (error) {
          console.error('❌ Error syncing data:', error);
          // Fallback to local data
          const localPlan = loadReadingPlan();
          if (localPlan) {
            setReadingPlan(localPlan);
            const task = getCurrentReadingTask(localPlan);
            setCurrentReadingTask(task);
          }
        }
      };

      syncData();
    }
  }, [userData]);

  // Function to fetch streak data from backend
  const fetchStreakData = async () => {
    try {
      if (!userData || !userData.id) return;

      const response = await fetch(API_ENDPOINTS.GET_STREAK(userData.id), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const streakInfo = await response.json();
        console.log('📊 Retrieved streak data from backend:', streakInfo);
        console.log('📊 Backend response keys:', Object.keys(streakInfo));
        console.log('📊 currentstreak value:', streakInfo.currentstreak);
        console.log('📊 maxstreak value:', streakInfo.maxstreak);

        const newStreakData = {
          currentStreak: streakInfo.currentstreak || 0,
          maxStreak: streakInfo.maxstreak || 0
        };

        console.log('📊 Setting streak data to:', newStreakData);
        setStreakData(newStreakData);
      } else {
        console.error('❌ Failed to fetch streak data');
      }
    } catch (error) {
      console.error('❌ Error fetching streak data:', error);
    }
  };

  // Function to fetch last read data from backend
  const fetchLastReadData = async () => {
    try {
      if (!userData || !userData.id) return;

      const response = await fetch(API_ENDPOINTS.GET_USER(userData.id), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const userInfo = await response.json();
        if (userInfo.lastSurah && userInfo.lastAyah) {
          console.log(`📖 Retrieved from backend: LastSurah=${userInfo.lastSurah}, LastAyah=${userInfo.lastAyah}`);

          // Find Surah info from our array
          const surahInfo = SURAH_PAGE_RANGES.find(item => item.surah === userInfo.lastSurah);

          // Get Surah name from API
          try {
            const surahResponse = await fetch('https://api.alquran.cloud/v1/surah');
            if (surahResponse.ok) {
              const surahData = await surahResponse.json();
              const surahApiInfo = surahData.data.find(s => s.number === userInfo.lastSurah);

              setLastRead({
                surahName: surahApiInfo?.englishName || 'Al-Fatihah',
                lastPage: userInfo.lastAyah || 1,        // This is the start page from our array
                surahId: userInfo.lastSurah || 1,
                startPage: surahInfo?.start || 1,        // Store the start page for reference
                endPage: surahInfo?.end || 1             // Store the end page for reference
              });

              console.log(`📖 Last Read set: ${surahApiInfo?.englishName}, Page ${userInfo.lastAyah}`);
            }
          } catch (error) {
            console.error('Error fetching Surah names:', error);
            setLastRead({
              surahName: 'Al-Fatihah',
              lastPage: userInfo.lastAyah || 1,
              surahId: userInfo.lastSurah || 1,
              startPage: surahInfo?.start || 1,
              endPage: surahInfo?.end || 1
            });
          }
        }
      }
    } catch (error) {
      console.error('❌ Error fetching last read data:', error);
    }
  };

  // Function to handle Last Read click
  const handleLastReadClick = () => {
    if (lastRead.surahId) {
      // Navigate to the Surah page with the specific page number (lastAyah from backend)
      const pageNumber = lastRead.lastPage; // This is the start page from our array
      console.log(`📖 Navigating to Surah ${lastRead.surahId}, Page ${pageNumber}`);
      navigate(`/surahayat/${lastRead.surahId}?page=${pageNumber}`);
    }
  };

  // Handlers for completion feature
  const handleCompletionClick = () => {
    if (readingPlan) {
      // If plan exists, show completion view
      setShowCompletionView(true);
    } else {
      // If no plan, show modal to create one
      setShowCompletionModal(true);
    }
  };

  const handlePlanCreated = (newPlan) => {
    console.log('📋 Plan created, updating state...', newPlan);
    console.log('📋 Plan details:', {
      startDate: newPlan.startDate,
      totalDays: newPlan.totalDays,
      pagesPerDay: newPlan.pagesPerDay,
      completedDays: newPlan.completedDays
    });

    // Calculate the reading task immediately
    const task = getCurrentReadingTask(newPlan);
    console.log('📋 Calculated reading task:', task);

    if (!task) {
      console.error('❌ Failed to calculate reading task for new plan!');
      console.log('📋 Plan data for debugging:', JSON.stringify(newPlan, null, 2));
    }

    // Update all state synchronously
    setReadingPlan(newPlan);
    setCurrentReadingTask(task);
    setShowCompletionModal(false);
    setShowCompletionView(true);

    console.log('📋 State updated - Plan set:', !!newPlan, 'Task set:', !!task);
  };

  const handleTaskCompleted = (updatedPlan) => {
    if (updatedPlan === null) {
      // Plan was completed and auto-deleted
      console.log('📝 Plan completed and removed');
      setReadingPlan(null);
      setCurrentReadingTask(null);
    } else {
      // Normal task completion
      setReadingPlan(updatedPlan);
      const task = getCurrentReadingTask(updatedPlan);
      setCurrentReadingTask(task);
    }
  };

  const handlePlanCanceled = () => {
    setReadingPlan(null);
    setCurrentReadingTask(null);
  };


  const pillars = [
    {
      title: "Shahadah",
      subtitle: "(Faith)",
      description: "To believe in no God but Allah and that Muhammad is his prophet.",
      image: ShahadahImg,
    },
    {
      title: "Salah",
      subtitle: "(Prayer)",
      description: "Performing the five daily prayers on time and with devotion.",
      image: SalahImg,
    },
    {
      title: "Sawm",
      subtitle: "(Fasting)",
      description: "Fasting during the month of Ramadan as an act of worship.",
      image: SawmImg,
    },
    {
      title: "Zakat",
      subtitle: "(Almsgiving)",
      description: "Giving a portion of wealth to those in need and the poor.",
      image: ZakatImg,
    },
    {
      title: "Hajj",
      subtitle: "(Pilgrimage)",
      description: "Pilgrimage to Mecca at least once in a lifetime if able.",
      image: HajjImg,
    },
  ];
  
  return (
    <div style={{ marginTop: '70px' }}>
      {/* Top Section with background image */}
      <div style={{
        width: '100%',
        minHeight: '600px',
        backgroundImage: `url(${Bsg})`,
        backgroundRepeat: 'no-repeat',

        backgroundSize: 'cover',
        backgroundPosition: 'center',
        position: 'relative',
        padding: '20px',
        boxSizing: 'border-box',
      }}>


  <h1 style={{
    textAlign: "center",
    fontSize: "36px",
    color: "#d4af37",
    fontWeight: "bold",
    marginBottom: "20px",
    marginTop: "50px",
    lineHeight: "1.5",
  }}>
       Welcome to Quran Memorization Center
  </h1>
  <h3 style={{ textAlign: "center", fontSize: "18px", color: "#bfa131", marginBottom: "10px" }}>
  In the Name of Allah<br />The Most Gracious, The Most Merciful
  </h3>


  {/* Content container */}
<div style={{
  position: 'relative',
  zIndex: 2,
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'flex-start',
  width: '100%',
  maxWidth: '1200px',
  margin: '60px auto 0 auto',
  padding: '0 20px',
}}>
  
  {/* Left Section */}
  <div style={{ flex: '1', marginRight: '20px' }}>
    <h3 style={{ marginBottom: '20px', fontSize: '28px', color: '#bfa131' }}>Streaks</h3>
    <div style={{ display: 'flex', gap: '20px', marginBottom: '40px', flexWrap: 'wrap' }}>
      {(() => {
        console.log('📊 Current streakData state:', streakData);
        return [
          { title: 'Current Streak', value: `${streakData.currentStreak} Days` },
          { title: 'Longest Streak', value: `${streakData.maxStreak} Days` }
        ];
      })().map((item, index) => (
        <div
          key={index}
          style={{
            backgroundColor: '#f5f5f5',
            padding: '20px',
            borderRadius: '10px',
            flex: '1 1 140px',
            maxWidth: '200px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
            transition: 'all 0.3s ease-in-out',
            cursor: 'pointer',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'scale(1.05)';
            e.currentTarget.style.boxShadow = '0 8px 20px rgba(0,0,0,0.4)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'scale(1)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';
          }}
        >
          <h5 style={{ fontSize: '20px', marginBottom: '10px', color: '#bfa131' }}>{item.title}</h5>
          <p style={{ color: '#d4af37', fontSize: '18px', fontWeight: 'bold' }}>{item.value}</p>
        </div>
      ))}
    </div>

    <h3 style={{ marginBottom: '20px', fontSize: '28px', color: '#bfa131' }}>Progress</h3>

    {/* Completion Section */}
    <div
      onClick={handleCompletionClick}
      style={{
        backgroundColor: '#f5f5f5',
        padding: '15px 20px',
        borderRadius: '10px',
        marginBottom: '15px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        maxWidth: '400px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
        transition: 'all 0.3s ease-in-out',
        cursor: 'pointer',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'scale(1.05)';
        e.currentTarget.style.boxShadow = '0 8px 20px rgba(0,0,0,0.4)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'scale(1)';
        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';
      }}
    >
      <span style={{ color: '#bfa131' }}>Completion</span>
      <span style={{ color: '#d4af37', fontWeight: 'bold' }}>
        {readingPlan ? `${calculateCompletionPercentage(readingPlan)}%` : 'No Plan'}
      </span>
    </div>



    {/* Memorization Section (unchanged) */}
    <div
      style={{
        backgroundColor: '#f5f5f5',
        padding: '15px 20px',
        borderRadius: '10px',
        marginBottom: '15px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        maxWidth: '400px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
        transition: 'all 0.3s ease-in-out',
        cursor: 'pointer',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'scale(1.05)';
        e.currentTarget.style.boxShadow = '0 8px 20px rgba(0,0,0,0.4)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'scale(1)';
        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';
      }}
    >
      <span style={{ color: '#bfa131' }}>Memorization</span>
      <span style={{ color: '#d4af37', fontWeight: 'bold' }}>40%</span>
    </div>


  </div>

  {/* Right Section */}
  <div style={{ flex: '1', display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
    <div style={{ marginBottom: '20px', textAlign: 'center', width: '100%' }}>
      <p style={{ margin: 0, fontSize: '14px', color: '#bfa131' }}>Assalamu Alaikum</p>
      <h4 style={{ margin: 0, color: '#d4af37' }}>{userData?.userName || 'Guest'}</h4>
    </div>

    <div
      onClick={handleLastReadClick}
      style={{
        backgroundColor: '#f5f5f5',
        borderRadius: '15px',
        padding: '20px',
        display: 'flex',
        alignItems: 'center',
        width: '100%',
        maxWidth: '500px',
        height: '250px',
        position: 'relative',
        overflow: 'hidden',
        boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
        transition: 'all 0.3s ease-in-out',
        cursor: 'pointer',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'scale(1.05)';
        e.currentTarget.style.boxShadow = '0 8px 20px rgba(0,0,0,0.4)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'scale(1)';
        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';
      }}
    >
      <div style={{ zIndex: 1 }}>
        <p style={{ fontSize: '14px', marginBottom: '10px', color: '#bfa131' }}>Last Read</p>
        <h4 style={{ marginBottom: '5px', color: '#d4af37' }}>{lastRead.surahName}</h4>
        <p style={{ fontSize: '14px', color: '#bfa131' }}>Page: {lastRead.lastPage}</p>
      </div>

      <img
        src={QuranImage}
        alt="Quran"
        style={{
          position: 'absolute',
          right: '-20px',
          bottom: '-20px',
          width: '270px',
          height: '270px',
          objectFit: 'contain',
          opacity: 0.9,
        }}
      />
    </div>
  </div>
</div></div>


      {/* Prayer Times Section */}
      <div style={{ padding: '60px 20px', backgroundColor: '#ffffff' }}>
        <h3 style={{ textAlign: 'center', fontSize: '18px', color: '#bfa131', marginBottom: '10px' }}>
          Prayer Times in Egypt
        </h3>

        <h1 style={{
          textAlign: 'center',
          fontSize: '36px',
          color: '#d4af37',
          fontWeight: 'bold',
          marginBottom: '50px',
          lineHeight: '1.5',
        }}>
          Our Prayer Timings
        </h1>

        {loading ? (
          <p style={{ textAlign: 'center', color: '#bfa131' }}>Loading prayer times...</p>
        ) : (
          <div style={{
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'center',
            gap: '30px',
            maxWidth: '1200px',
            margin: '0 auto',
          }}>
            {prayerTimes.map((prayer, index) => (
              <div key={index} style={{
                width: '200px',
                height: '200px',
                backgroundColor: '#f5f5f5',
                borderRadius: '20px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                textAlign: 'center',
                color: '#bfa131',
                transition: 'all 0.3s ease-in-out',
                cursor: 'pointer',
              }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'scale(1.05)';
                  e.currentTarget.style.boxShadow = '0 4px 20px rgba(0,0,0,0.4)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'scale(1)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';
                }}
              >
                <h3 style={{ fontSize: '22px', marginBottom: '10px', fontWeight: 'bold' }}>
                  {prayer.name}
                </h3>
                <p style={{ fontSize: '14px', margin: '5px 0', color: '#bfa131' }}>
                  Time: {prayer.time}
                </p>
              </div>
            ))}
          </div>
        )}
      </div>{/* Vision & Mission Section */}
<div style={{ marginTop: "30px", marginBottom: "30px", backgroundColor: "#f5f5f5", padding: "90px 20px" }}>

  <h1 style={{
    textAlign: "center",
    fontSize: "36px",
    color: "#d4af37",
    fontWeight: "bold",
    marginBottom: "50px",
    lineHeight: "1.5",
  }}>
  The Quran Center's Vision And Mission
  </h1>

  <div style={{
    display: "flex",
    justifyContent: "center",
    alignItems: "flex-start",
    gap: "80px",
    flexWrap: "wrap",
    maxWidth: "1200px",
    margin: "0 auto",
  }}>
    {/* Left Text in Vision/mission */}
    <div style={{ flex: "1 1 500px", color: "#333", fontSize: "15px", lineHeight: "1.8" }}>
      <p>
        Our center is dedicated to helping Muslims memorize the Holy Quran using
        advanced Artificial Intelligence (AI) technology. Through intelligent voice recognition,
        Tajweed correction algorithms, and personalized memorization plans,
        we enable students to perfect their Quran recitation without relying on traditional teachers.
        {/* You need to define showFullText in your component state if you want this toggle to work */}
      </p>

      <div style={{ marginTop: "30px" }}>
        <button style={{
          backgroundColor: "transparent",
          color: "#bfa131",
          border: "2px solid #d4af37",
          borderRadius: "30px",
          padding: "10px 30px",
          fontWeight: "bold",
          fontSize: "14px",
          cursor: "pointer",
          boxShadow: "0px 4px 8px rgba(0,0,0,0.3)",
          transition: "all 0.3s ease-in-out",
        }}
          onClick={() => setShowFullText(prev => !prev)}
          onMouseOver={(e) => {
            e.target.style.backgroundColor = "#d4af37";
            e.target.style.color = "#ffffff";
          }}
          onMouseOut={(e) => {
            e.target.style.backgroundColor = "transparent";
            e.target.style.color = "#bfa131";
          }}
        >
          {showFullText ? "Show Less" : "Read More"}
        </button>
      </div>

      {showFullText && (
        <p style={{ marginTop: "10px" }}>
          We strive to make Quranic education available to every Muslim child and adult,
          wherever they are. Using AI, students can receive instant feedback, track their memorization journey,
          and enjoy a more interactive and motivating learning experience.
        </p>
      )}
    </div>

    {/* Right Side: Vision + Mission */}
    <div style={{ flex: "1 1 400px", display: "flex", flexDirection: "column", gap: "40px" }}>
      {/* Vision */}
      <div style={{ display: "flex", alignItems: "center", gap: "20px" }}>
        <div style={{
          width: "90px",
          height: "90px",
          backgroundColor: "#d4af37",
          borderRadius: "50%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          boxShadow: "0px 4px 10px rgba(0,0,0,0.2)"
        }}>
          <img
            src={VisionIcon}
            alt="Vision"
            style={{
              width: "50px",
              height: "50px",
              objectFit: "contain"
            }}
          />
        </div>
        <div>
          <h4 style={{ marginBottom: "5px", color: "#bfa131" }}>Our Vision</h4>
          <p style={{ color: "#555", fontSize: "14px", lineHeight: "1.6" }}>
            To build a generation of Muslims who carry the Quran in their hearts,
            live by its guidance, and spread its light across the world.
          </p>
        </div>
      </div>

      {/* Mission */}
      <div style={{ display: "flex", alignItems: "center", gap: "20px" }}>
        <div style={{
          width: "90px",
          height: "90px",
          backgroundColor: "#d4af37",
          borderRadius: "50%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          boxShadow: "0px 4px 10px rgba(0,0,0,0.2)"
        }}>
          <img
            src={MissionIcon}
            alt="Mission"
            style={{
              width: "50px",
              height: "50px",
              objectFit: "contain"
            }}
          />
        </div>
        <div>
          <h4 style={{ marginBottom: "5px", color: "#bfa131" }}>Our Mission</h4>
          <p style={{ color: "#555", fontSize: "14px", lineHeight: "1.6" }}>
            To provide high-quality Quranic education through structured memorization plans,
            Tajweed perfection, and building strong Islamic character among students.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
{/* Pillars of Islam Section */}
<div style={{ marginTop: '10px', padding: '140px 20px' ,backgroundColor:'white'}}>
  <h2 style={{
    textAlign: 'center',
    marginBottom: '60px',
    fontSize: '36px',
    fontWeight: 'bold',
    color: '#bfa131',
  }}>
    Pillars of Islam
  </h2>

  <div style={{
    display: 'flex',
    justifyContent: 'center',
    gap: '40px',
    flexWrap: 'wrap',
  }}>
    {pillars.map((pillar, index) => (
      <div
        key={index}
        style={{
          backgroundColor: '#f5f5f5', // soft light background like your cards
          borderRadius: '20px',
          width: '200px',
          padding: '30px 20px',
          textAlign: 'center',
          color: '#bfa131',
          boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
          transition: 'all 0.3s ease-in-out',
          cursor: 'pointer',
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'scale(1.07)';
          e.currentTarget.style.boxShadow = '0 4px 20px rgba(0,0,0,0.4)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'scale(1)';
          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';
        }}
      >
        {/* Image Circle */}
        <div style={{
          width: '100px',
          height: '100px',
          margin: '0 auto 20px auto',
          borderRadius: '50%',
          backgroundColor: '#d4af37',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          overflow: 'hidden',
          boxShadow: '0 4px 10px rgba(0,0,0,0.3)',
        }}>
          <img
            src={pillar.image}
            alt={pillar.title}
            style={{
              width: '120px',
              height: '120px',
              objectFit: 'cover',
              objectPosition: 'center',
            }}
          />
        </div>

        {/* Title */}
        <h4 style={{
          marginBottom: '5px',
          fontWeight: 'bold',
          fontSize: '18px',
        }}>
          {pillar.title}
        </h4>

        {/* Subtitle */}
        <p style={{
          marginBottom: '10px',
          fontSize: '13px',
          color: '#bfa131',
        }}>
          {pillar.subtitle}
        </p>

        {/* Description */}
        <p style={{
          fontSize: '12px',
          color: '#555', // darker gray for better readability
        }}>
          {pillar.description}
        </p>
      </div>
    ))}
  </div>
</div>

      {/* Completion Modal */}
      <CompletionModal
        isOpen={showCompletionModal}
        onClose={() => setShowCompletionModal(false)}
        onPlanCreated={handlePlanCreated}
      />

      {/* Completion View Modal */}
      <CompletionView
        isOpen={showCompletionView}
        onClose={() => setShowCompletionView(false)}
        readingTask={currentReadingTask}
        planData={readingPlan}
        onTaskCompleted={handleTaskCompleted}
        onPlanCanceled={handlePlanCanceled}
      />

    </div>
  );
}


export default Home;
