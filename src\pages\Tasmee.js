import React from 'react';

function Tasmee() {
  return (
    <div style={{ 
      marginTop: '70px',
      minHeight: '100vh',
      padding: '40px 20px',
      backgroundColor: '#f8f9fa'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        textAlign: 'center'
      }}>
        <h1 style={{
          fontSize: '36px',
          color: '#d4af37',
          fontWeight: 'bold',
          marginBottom: '20px'
        }}>
          Tasmee - Quran Recitation
        </h1>
        
        <p style={{
          fontSize: '18px',
          color: '#666',
          marginBottom: '40px',
          lineHeight: '1.6'
        }}>
          Practice and perfect your Quran recitation with our AI-powered Tasmee feature.
        </p>

        <div style={{
          backgroundColor: 'white',
          borderRadius: '15px',
          padding: '40px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
          border: '1px solid rgba(212, 175, 55, 0.1)'
        }}>
          <h2 style={{
            color: '#8B7355',
            marginBottom: '20px',
            fontSize: '24px'
          }}>
            Coming Soon
          </h2>
          
          <p style={{
            color: '#666',
            fontSize: '16px',
            lineHeight: '1.6'
          }}>
            The Tasmee feature is currently under development. 
            This will allow you to practice Quran recitation with AI-powered feedback 
            to help improve your pronunciation and Tajweed.
          </p>
        </div>
      </div>
    </div>
  );
}

export default Tasmee;
